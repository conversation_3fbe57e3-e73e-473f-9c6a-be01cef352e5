<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Book Series Management
 */

require_once '../config/config.php';
require_once '../includes/Database.php';
require_once '../includes/functions.php';

// Check if user is logged in
require_admin();

// Initialize database
$db = new Database();

// Handle actions
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? null;
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
            case 'edit':
                $name = trim($_POST['name'] ?? '');
                $nameNepali = trim($_POST['nameNepali'] ?? '') ?: null;
                $description = trim($_POST['description'] ?? '') ?: null;
                $totalBooks = intval($_POST['totalBooks'] ?? 0) ?: null;
                $isComplete = isset($_POST['isComplete']) ? 1 : 0;
                $isActive = isset($_POST['isActive']) ? 1 : 0;
                
                // Validation
                if (empty($name)) {
                    $error = 'Series name is required';
                } elseif ($totalBooks !== null && $totalBooks < 1) {
                    $error = 'Total books must be a positive number';
                } else {
                    try {
                        if ($_POST['action'] === 'add') {
                            // Check if series already exists
                            $existing = $db->fetch("SELECT id FROM book_series WHERE name = ? AND isDeleted = 0", [$name]);
                            if ($existing) {
                                $error = 'Series with this name already exists';
                            } else {
                                $series_id = 'ser' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
                                $data = [
                                    'id' => $series_id,
                                    'name' => $name,
                                    'nameNepali' => $nameNepali,
                                    'description' => $description,
                                    'totalBooks' => $totalBooks,
                                    'isComplete' => $isComplete,
                                    'isActive' => $isActive,
                                    'createdAt' => date('Y-m-d H:i:s'),
                                    'updatedAt' => date('Y-m-d H:i:s')
                                ];
                                $db->insert('book_series', $data);
                                $message = 'Series added successfully';
                                // log_activity("Added series: $name", 'INFO');
                                $action = 'list';
                            }
                        } else {
                            // Edit existing series
                            $edit_id = $_POST['id'] ?? '';
                            if ($edit_id) {
                                // Check if name conflicts with other series
                                $existing = $db->fetch("SELECT id FROM book_series WHERE name = ? AND id != ? AND isDeleted = 0", [$name, $edit_id]);
                                if ($existing) {
                                    $error = 'Series with this name already exists';
                                } else {
                                    $data = [
                                        'name' => $name,
                                        'nameNepali' => $nameNepali,
                                        'description' => $description,
                                        'totalBooks' => $totalBooks,
                                        'isComplete' => $isComplete,
                                        'isActive' => $isActive,
                                        'updatedAt' => date('Y-m-d H:i:s')
                                    ];
                                    $db->update('book_series', $data, 'id = ?', [$edit_id]);
                                    $message = 'Series updated successfully';
                                    // log_activity("Updated series: $name", 'INFO');
                                    $action = 'list';
                                }
                            }
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                        // log_activity("Series save error: " . $e->getMessage(), 'ERROR');
                    }
                }
                break;
                
            case 'delete':
                $delete_id = $_POST['id'] ?? '';
                if ($delete_id) {
                    try {
                        // Check if series has books
                        $book_count = $db->fetchColumn("SELECT COUNT(*) FROM books WHERE seriesId = ? AND isDeleted = 0", [$delete_id]);
                        if ($book_count > 0) {
                            $error = "Cannot delete series. $book_count books are associated with this series.";
                        } else {
                            $series_name = $db->fetchColumn("SELECT name FROM book_series WHERE id = ?", [$delete_id]);
                            $db->delete('book_series', 'id = ?', [$delete_id]);
                            $message = 'Series deleted successfully';
                            // log_activity("Deleted series: $series_name", 'INFO');
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                        // log_activity("Series delete error: " . $e->getMessage(), 'ERROR');
                    }
                }
                break;
        }
    }
}

// Handle AJAX delete request
if (isset($_GET['ajax_delete']) && $_GET['ajax_delete'] === '1' && $id) {
    header('Content-Type: application/json');
    try {
        // Check if series has books
        $book_count = $db->fetchColumn("SELECT COUNT(*) FROM books WHERE seriesId = ? AND isDeleted = 0", [$id]);
        if ($book_count > 0) {
            echo json_encode(['success' => false, 'message' => "Cannot delete series. $book_count books are associated with this series."]);
        } else {
            $series_name = $db->fetchColumn("SELECT name FROM book_series WHERE id = ?", [$id]);
            $db->delete('book_series', 'id = ?', [$id]);
            // log_activity("Deleted series: $series_name", 'INFO');
            echo json_encode(['success' => true, 'message' => 'Series deleted successfully']);
        }
    } catch (Exception $e) {
        // log_activity("Series delete error: " . $e->getMessage(), 'ERROR');
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
    exit;
}

// Get data for edit form
$series_data = null;
if ($action === 'edit' && $id) {
    $series_data = $db->fetch("SELECT * FROM book_series WHERE id = ? AND isDeleted = 0", [$id]);
    if (!$series_data) {
        $error = 'Series not found';
        $action = 'list';
    }
}

// Get series list for list view
$series_list = [];
$total_series = 0;
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = ADMIN_ITEMS_PER_PAGE;
$search = trim($_GET['search'] ?? '');
$status_filter = $_GET['status'] ?? '';
$completion_filter = $_GET['completion'] ?? '';

if ($action === 'list') {
    try {
        $where_conditions = ['s.isDeleted = 0'];
        $params = [];

        if (!empty($search)) {
            $where_conditions[] = '(s.name LIKE ? OR s.nameNepali LIKE ? OR s.description LIKE ?)';
            $search_param = "%$search%";
            $params[] = $search_param;
            $params[] = $search_param;
            $params[] = $search_param;
        }

        if ($status_filter === 'active') {
            $where_conditions[] = 's.isActive = 1';
        } elseif ($status_filter === 'inactive') {
            $where_conditions[] = 's.isActive = 0';
        }

        if ($completion_filter === 'completed') {
            $where_conditions[] = 's.isComplete = 1';
        } elseif ($completion_filter === 'ongoing') {
            $where_conditions[] = 's.isComplete = 0';
        }

        $where_clause = implode(' AND ', $where_conditions);

        // Get total count
        $total_series = $db->fetchColumn("
            SELECT COUNT(*)
            FROM book_series s
            WHERE $where_clause
        ", $params);

        // Get series with book count
        $offset = ($page - 1) * $per_page;
        $series_list = $db->fetchAll("
            SELECT s.*,
                   COUNT(b.id) as bookCount
            FROM book_series s
            LEFT JOIN books b ON s.id = b.seriesId AND b.isDeleted = 0
            WHERE $where_clause
            GROUP BY s.id
            ORDER BY s.name ASC
            LIMIT $per_page OFFSET $offset
        ", $params);
        
    } catch (Exception $e) {
        $error = 'Database error: ' . $e->getMessage();
        // log_activity("Series list error: " . $e->getMessage(), 'ERROR');
    }
}

$page_title = 'Book Series - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="<?= asset_url('css/style.css') ?>">
    <link rel="stylesheet" href="<?= asset_url('css/admin.css') ?>">
    
    <!-- Favicon and App Icons -->
    <?= generate_favicon_tags() ?>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <button class="mobile-close-btn" id="mobile-close-btn">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <div class="sidebar-logo">
                    <img src="<?= icon_url(48) ?>" alt="<?= APP_NAME ?> Icon" class="sidebar-logo-image">
                </div>
                <h2 class="sidebar-title">Admin Panel</h2>
                <p class="sidebar-subtitle">मैथिली विकास कोष</p>
            </div>

            <!-- Mobile User Section -->
            <div class="mobile-user-section">
                <div class="mobile-user-info">
                    <div class="mobile-user-avatar">
                        <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                    </div>
                    <div class="mobile-user-details">
                        <span class="mobile-user-name"><?= htmlspecialchars($_SESSION['admin_name']) ?></span>
                        <span class="mobile-user-role">Administrator</span>
                    </div>
                </div>
                <a href="logout.php" class="mobile-logout-btn">
                    <span class="logout-icon">🚪</span>
                    Logout
                </a>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <div class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Dashboard
                        </a>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Book Management</div>
                    <div class="nav-item">
                        <a href="books.php" class="nav-link">
                            <span class="nav-icon">📚</span>
                            Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="books.php?action=add" class="nav-link">
                            <span class="nav-icon">➕</span>
                            Add Book
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-books.php" class="nav-link">
                            <span class="nav-icon">📥</span>
                            Import Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-images.php" class="nav-link">
                            <span class="nav-icon">🖼️</span>
                            Import Images
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Catalog Management</div>
                    <div class="nav-item">
                        <a href="authors.php" class="nav-link">
                            <span class="nav-icon">✍️</span>
                            Authors
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="categories.php" class="nav-link">
                            <span class="nav-icon">🏷️</span>
                            Categories
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="publishers.php" class="nav-link">
                            <span class="nav-icon">🏢</span>
                            Publishers
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="languages.php" class="nav-link">
                            <span class="nav-icon">🌐</span>
                            Languages
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="subjects.php" class="nav-link">
                            <span class="nav-icon">📖</span>
                            Subjects
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="series.php" class="nav-link active">
                            <span class="nav-icon">📑</span>
                            Book Series
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="locations.php" class="nav-link">
                            <span class="nav-icon">📍</span>
                            Locations
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="conditions.php" class="nav-link">
                            <span class="nav-icon">🔧</span>
                            Conditions
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="sources.php" class="nav-link">
                            <span class="nav-icon">🎁</span>
                            Sources
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Reports & Analytics</div>
                    <div class="nav-item">
                        <a href="reports.php" class="nav-link">
                            <span class="nav-icon">📈</span>
                            Collection Reports
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="analytics.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Analytics
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <div class="nav-item">
                        <a href="users.php" class="nav-link">
                            <span class="nav-icon">👤</span>
                            Admin Users
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="settings.php" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            Settings
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="backup.php" class="nav-link">
                            <span class="nav-icon">💾</span>
                            Backup & Export
                        </a>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <header class="admin-header">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="header-title">
                            <span class="title-icon">📑</span>
                            Book Series Management
                        </h1>
                        <p class="header-subtitle">Manage book series and collections</p>
                    </div>
                    <div class="header-actions">
                        <div class="user-info">
                            <span class="welcome-text">Welcome, <?= htmlspecialchars($_SESSION['admin_name']) ?>!</span>
                            <div class="user-menu">
                                <div class="user-avatar">
                                    <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                                </div>
                            </div>
                        </div>
                        <a href="logout.php" class="btn btn-secondary btn-sm">
                            <span style="margin-right: 0.5rem;">🚪</span>
                            Logout
                        </a>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Breadcrumb -->
                <nav class="breadcrumb">
                    <a href="dashboard.php" class="breadcrumb-item">Dashboard</a>
                    <span class="breadcrumb-separator">›</span>
                    <span class="breadcrumb-item active">Book Series</span>
                </nav>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <span class="alert-icon">✅</span>
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <span class="alert-icon">❌</span>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <?php if ($action === 'list'): ?>
                    <!-- Series List -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <h2 class="page-title">Book Series</h2>
                            <p class="page-subtitle">Manage book series and collections</p>
                        </div>
                        <div class="page-header-actions">
                            <a href="?action=add" class="btn btn-primary">
                                <span style="margin-right: 0.5rem;">➕</span>
                                Add Series
                            </a>
                        </div>
                    </div>

                    <!-- Search and Filters -->
                    <div class="card">
                        <div class="card-body">
                            <form method="GET" class="search-form">
                                <div class="search-form-row">
                                    <div class="search-input-group">
                                        <input type="text" name="search" value="<?= htmlspecialchars($search) ?>"
                                               placeholder="Search series by name or description..." class="input">
                                        <select name="status" class="input">
                                            <option value="">All Status</option>
                                            <option value="active" <?= $status_filter === 'active' ? 'selected' : '' ?>>Active</option>
                                            <option value="inactive" <?= $status_filter === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                        </select>
                                        <select name="completion" class="input">
                                            <option value="">All Series</option>
                                            <option value="completed" <?= $completion_filter === 'completed' ? 'selected' : '' ?>>Completed</option>
                                            <option value="ongoing" <?= $completion_filter === 'ongoing' ? 'selected' : '' ?>>Ongoing</option>
                                        </select>
                                        <button type="submit" class="btn btn-secondary">
                                            <span style="margin-right: 0.5rem;">🔍</span>
                                            Search
                                        </button>
                                    </div>
                                    <?php if (!empty($search) || !empty($status_filter) || !empty($completion_filter)): ?>
                                        <a href="?" class="btn btn-outline">Clear</a>
                                    <?php endif; ?>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Series Table -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                Series List
                                <?php if (!empty($search)): ?>
                                    <span class="text-muted">(Search: "<?= htmlspecialchars($search) ?>")</span>
                                <?php endif; ?>
                            </h3>
                            <div class="card-header-actions">
                                <span class="text-muted">Total: <?= number_format($total_series) ?> series</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($series_list)): ?>
                                <div class="empty-state">
                                    <div class="empty-state-icon">📑</div>
                                    <h3 class="empty-state-title">No Series Found</h3>
                                    <p class="empty-state-description">
                                        <?php if (!empty($search) || !empty($status_filter) || !empty($completion_filter)): ?>
                                            No series match your search criteria. Try adjusting your search terms or filters.
                                        <?php else: ?>
                                            Start by adding your first book series to organize related books.
                                        <?php endif; ?>
                                    </p>
                                    <div class="empty-state-actions">
                                        <?php if (!empty($search) || !empty($status_filter) || !empty($completion_filter)): ?>
                                            <a href="?" class="btn btn-secondary">Clear Filters</a>
                                        <?php endif; ?>
                                        <a href="?action=add" class="btn btn-primary">Add First Series</a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Volumes</th>
                                                <th>Books</th>
                                                <th>Completion</th>
                                                <th>Status</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($series_list as $series): ?>
                                                <tr>
                                                    <td>
                                                        <div class="series-name">
                                                            <strong><?= htmlspecialchars($series['name']) ?></strong>
                                                            <?php if (!empty($series['nameNepali'])): ?>
                                                                <div class="series-name-nepali">
                                                                    <span class="nepali-text"><?= htmlspecialchars($series['nameNepali']) ?></span>
                                                                </div>
                                                            <?php endif; ?>
                                                            <?php if (!empty($series['description'])): ?>
                                                                <div class="series-description">
                                                                    <?= htmlspecialchars(substr($series['description'], 0, 80)) ?>
                                                                    <?php if (strlen($series['description']) > 80): ?>...<?php endif; ?>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <?php if ($series['totalBooks']): ?>
                                                            <span class="badge badge-info"><?= number_format($series['totalBooks']) ?> books</span>
                                                        <?php else: ?>
                                                            <span class="text-muted">—</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-secondary"><?= number_format($series['bookCount']) ?> books</span>
                                                    </td>
                                                    <td>
                                                        <?php if ($series['isComplete']): ?>
                                                            <span class="badge badge-success">✓ Completed</span>
                                                        <?php else: ?>
                                                            <span class="badge badge-warning">⏳ Ongoing</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if ($series['isActive']): ?>
                                                            <span class="badge badge-success">Active</span>
                                                        <?php else: ?>
                                                            <span class="badge badge-warning">Inactive</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="text-muted"><?= date('M j, Y', strtotime($series['createdAt'])) ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <a href="?action=edit&id=<?= urlencode($series['id']) ?>"
                                                               class="btn btn-sm btn-outline" title="Edit Series">
                                                                ✏️
                                                            </a>
                                                            <?php if ($series['bookCount'] == 0): ?>
                                                                <button type="button"
                                                                        class="btn btn-sm btn-danger delete-series"
                                                                        data-id="<?= htmlspecialchars($series['id']) ?>"
                                                                        data-name="<?= htmlspecialchars($series['name']) ?>"
                                                                        title="Delete Series">
                                                                    🗑️
                                                                </button>
                                                            <?php else: ?>
                                                                <button type="button"
                                                                        class="btn btn-sm btn-outline"
                                                                        disabled
                                                                        title="Cannot delete - has <?= $series['bookCount'] ?> books">
                                                                    🔒
                                                                </button>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <?php if ($total_series > $per_page): ?>
                                    <div class="pagination-wrapper">
                                        <?php
                                        $total_pages = ceil($total_series / $per_page);
                                        $query_params = $_GET;
                                        ?>
                                        <div class="pagination">
                                            <?php if ($page > 1): ?>
                                                <?php $query_params['page'] = $page - 1; ?>
                                                <a href="?<?= http_build_query($query_params) ?>" class="pagination-btn">‹ Previous</a>
                                            <?php endif; ?>

                                            <?php
                                            $start_page = max(1, $page - 2);
                                            $end_page = min($total_pages, $page + 2);

                                            for ($i = $start_page; $i <= $end_page; $i++):
                                                $query_params['page'] = $i;
                                            ?>
                                                <a href="?<?= http_build_query($query_params) ?>"
                                                   class="pagination-btn <?= $i === $page ? 'active' : '' ?>">
                                                    <?= $i ?>
                                                </a>
                                            <?php endfor; ?>

                                            <?php if ($page < $total_pages): ?>
                                                <?php $query_params['page'] = $page + 1; ?>
                                                <a href="?<?= http_build_query($query_params) ?>" class="pagination-btn">Next ›</a>
                                            <?php endif; ?>
                                        </div>
                                        <div class="pagination-info">
                                            Showing <?= number_format(($page - 1) * $per_page + 1) ?> to
                                            <?= number_format(min($page * $per_page, $total_series)) ?> of
                                            <?= number_format($total_series) ?> series
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                <?php elseif ($action === 'add' || $action === 'edit'): ?>
                    <!-- Add/Edit Series Form -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <h2 class="page-title"><?= $action === 'add' ? 'Add New Series' : 'Edit Series' ?></h2>
                            <p class="page-subtitle">
                                <?= $action === 'add' ? 'Enter series information to organize related books' : 'Update series information' ?>
                            </p>
                        </div>
                        <div class="page-header-actions">
                            <a href="?" class="btn btn-secondary">
                                <span style="margin-right: 0.5rem;">←</span>
                                Back to Series
                            </a>
                        </div>
                    </div>

                    <form method="POST" class="series-form">
                        <input type="hidden" name="action" value="<?= $action ?>">
                        <?php if ($action === 'edit' && $series_data): ?>
                            <input type="hidden" name="id" value="<?= htmlspecialchars($series_data['id']) ?>">
                        <?php endif; ?>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Series Information</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="name" class="label required">Series Name</label>
                                        <input type="text"
                                               id="name"
                                               name="name"
                                               class="input"
                                               placeholder="Enter series name"
                                               value="<?= htmlspecialchars($series_data['name'] ?? '') ?>"
                                               required>
                                        <div class="help-text">The primary name of the series (required)</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="nameNepali" class="label">Nepali/Maithili Name</label>
                                        <input type="text"
                                               id="nameNepali"
                                               name="nameNepali"
                                               class="input nepali-input"
                                               placeholder="श्रृंखलाको नाम"
                                               value="<?= htmlspecialchars($series_data['nameNepali'] ?? '') ?>">
                                        <div class="help-text">Series name in Nepali/Maithili script (optional)</div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="totalBooks" class="label">Total Books</label>
                                    <input type="number"
                                           id="totalBooks"
                                           name="totalBooks"
                                           class="input"
                                           placeholder="Enter total number of books"
                                           value="<?= htmlspecialchars($series_data['totalBooks'] ?? '') ?>"
                                           min="1">
                                    <div class="help-text">Expected total number of books in this series (optional)</div>
                                </div>

                                <div class="form-group">
                                    <label for="description" class="label">Description</label>
                                    <textarea id="description"
                                              name="description"
                                              class="textarea"
                                              rows="4"
                                              placeholder="Enter series description..."><?= htmlspecialchars($series_data['description'] ?? '') ?></textarea>
                                    <div class="help-text">Brief description of the series (optional)</div>
                                </div>

                                <div class="form-grid">
                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox"
                                                   name="isComplete"
                                                   value="1"
                                                   <?= ($series_data['isComplete'] ?? 0) ? 'checked' : '' ?>>
                                            <span class="checkbox-text">Series Completed</span>
                                        </label>
                                        <div class="help-text">Check if this series is complete</div>
                                    </div>

                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox"
                                                   name="isActive"
                                                   value="1"
                                                   <?= ($series_data['isActive'] ?? 1) ? 'checked' : '' ?>>
                                            <span class="checkbox-text">Active Series</span>
                                        </label>
                                        <div class="help-text">Uncheck to deactivate this series</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <span style="margin-right: 0.5rem;">💾</span>
                                <?= $action === 'add' ? 'Add Series' : 'Update Series' ?>
                            </button>
                            <a href="?" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Confirm Delete</h3>
                <button type="button" class="modal-close" onclick="closeDeleteModal()">×</button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the series "<span id="deleteSeriesName"></span>"?</p>
                <p class="text-warning">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeDeleteModal()">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">Delete Series</button>
            </div>
        </div>
    </div>

    <script>
        // Mobile Menu and Delete functionality
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            sidebar.classList.toggle('open');
            overlay.classList.toggle('active');
            document.body.style.overflow = sidebar.classList.contains('open') ? 'hidden' : '';
        }

        function closeMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            sidebar.classList.remove('open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        let deleteSeriesId = null;

        function openDeleteModal(id, name) {
            deleteSeriesId = id;
            document.getElementById('deleteSeriesName').textContent = name;
            document.getElementById('deleteModal').style.display = 'flex';
        }

        function closeDeleteModal() {
            deleteSeriesId = null;
            document.getElementById('deleteModal').style.display = 'none';
        }

        function confirmDelete() {
            if (deleteSeriesId) {
                fetch(`?ajax_delete=1&id=${encodeURIComponent(deleteSeriesId)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('Error: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while deleting the series.');
                    });
            }
            closeDeleteModal();
        }

        document.addEventListener('DOMContentLoaded', () => {
            // Event listeners
            document.getElementById('mobile-menu-toggle')?.addEventListener('click', toggleMobileMenu);
            document.getElementById('mobile-overlay')?.addEventListener('click', closeMobileMenu);
            document.getElementById('mobile-close-btn')?.addEventListener('click', closeMobileMenu);

            document.querySelectorAll('.delete-series').forEach(button => {
                button.addEventListener('click', function() {
                    openDeleteModal(this.getAttribute('data-id'), this.getAttribute('data-name'));
                });
            });

            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth <= 1023) closeMobileMenu();
                });
            });

            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    closeDeleteModal();
                    closeMobileMenu();
                }
            });
        });

        window.addEventListener('resize', () => {
            if (window.innerWidth > 1023) closeMobileMenu();
        });
    </script>
</body>
</html>
