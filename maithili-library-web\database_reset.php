<?php
/**
 * मैथिली विकास कोष - Complete Database Reset
 * This script performs a complete database reset while preserving admin users
 * Clears all data except users and reseeds with fresh default data
 */

require_once 'config/config.php';
require_once 'includes/Database.php';

// Set execution time limit for large operations
set_time_limit(600); // 10 minutes

// Check if confirmation is provided
$confirmed = isset($_POST['confirm_reset']) && $_POST['confirm_reset'] === 'RESET_DATABASE';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Reset - मैथिली विकास कोष</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #55080C 0%, #8B1538 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            color: #55080C;
            font-size: 2.2em;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header h2 {
            color: #666;
            font-size: 1.3em;
            font-weight: 400;
        }
        
        .warning-box {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border-left: 6px solid #ef4444;
        }
        
        .warning-box h3 {
            color: #ef4444;
            font-size: 1.4em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .warning-list {
            list-style: none;
            margin: 15px 0;
        }
        
        .warning-list li {
            padding: 8px 0;
            color: #666;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .confirm-form {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input[type="text"] {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input[type="text"]:focus {
            outline: none;
            border-color: #55080C;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s;
            text-align: center;
        }
        
        .btn-danger {
            background: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
            margin-right: 15px;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
            transform: translateY(-2px);
        }
        
        .progress-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .step {
            margin: 20px 0;
            padding: 20px;
            border-left: 4px solid #3b82f6;
            background: #f8fafc;
            border-radius: 0 8px 8px 0;
        }
        
        .step h3 {
            color: #1f2937;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .success {
            color: #22c55e;
            background: #f0fdf4;
            padding: 12px 15px;
            border-radius: 6px;
            margin: 8px 0;
            border-left: 4px solid #22c55e;
        }
        
        .error {
            color: #ef4444;
            background: #fef2f2;
            padding: 12px 15px;
            border-radius: 6px;
            margin: 8px 0;
            border-left: 4px solid #ef4444;
        }
        
        .info {
            color: #3b82f6;
            background: #eff6ff;
            padding: 12px 15px;
            border-radius: 6px;
            margin: 8px 0;
            border-left: 4px solid #3b82f6;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: #f8fafc;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #e5e7eb;
        }
        
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 13px;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .back-links {
            text-align: center;
            margin-top: 30px;
        }
        
        .back-links a {
            display: inline-block;
            margin: 0 10px;
        }
        
        .completion-box {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border-left: 6px solid #22c55e;
            text-align: center;
        }
        
        .completion-box h2 {
            color: #22c55e;
            font-size: 1.8em;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>मैथिली विकास कोष</h1>
            <h2>Complete Database Reset</h2>
        </div>

<?php if (!$confirmed): ?>
        <div class="warning-box">
            <h3>⚠️ Critical Warning</h3>
            <p style="margin-bottom: 15px; font-size: 1.1em; color: #ef4444; font-weight: 600;">
                This action will permanently delete ALL library data except user accounts!
            </p>
            
            <ul class="warning-list">
                <li>🗑️ All books and their records will be deleted</li>
                <li>🗑️ All authors, categories, and publishers will be removed</li>
                <li>🗑️ All members and borrowing records will be deleted</li>
                <li>🗑️ All locations, sources, and subjects will be cleared</li>
                <li>🗑️ All activity logs will be removed</li>
                <li>✅ User accounts will be preserved</li>
                <li>✅ Only essential book conditions will be added (6 items)</li>
            </ul>
            
            <p style="margin-top: 15px; color: #666;">
                <strong>Note:</strong> This action cannot be undone. Make sure you have a backup if needed.
            </p>
        </div>

        <div class="confirm-form">
            <form method="POST" action="">
                <div class="form-group">
                    <label for="confirm_text">
                        To confirm this action, type <strong style="color: #ef4444;">RESET_DATABASE</strong> in the box below:
                    </label>
                    <input type="text" id="confirm_text" name="confirm_reset" placeholder="Type RESET_DATABASE to confirm" required>
                </div>
                
                <div style="text-align: center;">
                    <a href="admin/" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-danger">🗑️ Reset Database</button>
                </div>
            </form>
        </div>
<?php else: ?>
        <div class="progress-container">
<?php
try {
    // PHASE 1: Database Connection
    echo "<div class='step'>\n";
    echo "<h3>🔌 Phase 1: Database Connection</h3>\n";

    $db = new Database();
    $conn = $db->getConnection();

    echo "<div class='success'>✅ Successfully connected to database</div>\n";
    echo "</div>\n";

    // PHASE 2: User Preservation Check
    echo "<div class='step'>\n";
    echo "<h3>👤 Phase 2: User Account Verification</h3>\n";

    $userCount = $conn->query("SELECT COUNT(*) as count FROM users")->fetch(PDO::FETCH_ASSOC)['count'];

    if ($userCount > 0) {
        echo "<div class='info'>ℹ️ Found {$userCount} user account(s) - these will be preserved</div>\n";

        // Show admin users
        $adminUsers = $conn->query("SELECT username, name, role FROM users WHERE role IN ('ADMIN', 'SUPER_ADMIN')")->fetchAll(PDO::FETCH_ASSOC);
        if (!empty($adminUsers)) {
            echo "<div class='info'>🔐 Admin users that will be preserved:</div>\n";
            foreach ($adminUsers as $admin) {
                echo "<div class='success'>  • {$admin['name']} ({$admin['username']}) - {$admin['role']}</div>\n";
            }
        }
    } else {
        echo "<div class='error'>❌ No users found - you may need to create an admin user after reset</div>\n";
    }
    echo "</div>\n";

    // PHASE 3: Clear Uploads Directory
    echo "<div class='step'>\n";
    echo "<h3>📁 Phase 3: Clearing Upload Files</h3>\n";

    $uploadsDir = __DIR__ . '/uploads';
    if (is_dir($uploadsDir)) {
        $files = glob($uploadsDir . '/*');
        $deletedCount = 0;

        foreach ($files as $file) {
            if (is_file($file) && basename($file) !== '.gitkeep') {
                if (unlink($file)) {
                    $deletedCount++;
                }
            }
        }

        echo "<div class='success'>✅ Cleared {$deletedCount} uploaded files</div>\n";
    } else {
        echo "<div class='info'>ℹ️ Uploads directory not found - skipping file cleanup</div>\n";
    }
    echo "</div>\n";

    // PHASE 4: Database Reset
    echo "<div class='step'>\n";
    echo "<h3>🗄️ Phase 4: Database Data Clearing</h3>\n";

    // Disable foreign key checks for smooth deletion
    $conn->exec("SET FOREIGN_KEY_CHECKS = 0");
    echo "<div class='info'>ℹ️ Disabled foreign key checks</div>\n";

    // Define tables to clear (in order to respect dependencies)
    $tablesToClear = [
        'borrowings' => 'Borrowing Records',
        'user_logs' => 'User Activity Logs',
        'image_import_files' => 'Image Import Files',
        'image_import_jobs' => 'Image Import Jobs',
        'import_errors' => 'Book Import Errors',
        'import_jobs' => 'Book Import Jobs',
        'books' => 'Books',
        'members' => 'Library Members',
        'subjects' => 'Subjects',
        'book_series' => 'Book Series',
        'locations' => 'Storage Locations',
        'sources' => 'Book Sources',
        'publishers' => 'Publishers',
        'categories' => 'Categories',
        'languages' => 'Languages',
        'authors' => 'Authors',
        'conditions' => 'Book Conditions'
    ];

    $clearedCounts = [];

    foreach ($tablesToClear as $table => $description) {
        try {
            // Check if table exists first
            $tableExists = $conn->query("SHOW TABLES LIKE '{$table}'")->rowCount() > 0;

            if (!$tableExists) {
                echo "<div class='info'>ℹ️ Skipped {$description}: Table does not exist</div>\n";
                $clearedCounts[$table] = 0;
                continue;
            }

            // Get count before deletion
            $countBefore = $conn->query("SELECT COUNT(*) FROM `{$table}`")->fetchColumn();

            // Clear the table
            $conn->exec("DELETE FROM `{$table}`");

            // Reset auto-increment if applicable
            $conn->exec("ALTER TABLE `{$table}` AUTO_INCREMENT = 1");

            $clearedCounts[$table] = $countBefore;
            echo "<div class='success'>✅ Cleared {$description}: {$countBefore} records</div>\n";

        } catch (PDOException $e) {
            echo "<div class='error'>❌ Error clearing {$description}: " . htmlspecialchars($e->getMessage()) . "</div>\n";
            $clearedCounts[$table] = 0;
        }
    }

    // Re-enable foreign key checks
    $conn->exec("SET FOREIGN_KEY_CHECKS = 1");
    echo "<div class='info'>ℹ️ Re-enabled foreign key checks</div>\n";
    echo "</div>\n";

    // PHASE 4.5: Clean up uploaded files
    echo "<div class='step'>\n";
    echo "<h3>🗂️ Phase 4.5: Cleaning Up Uploaded Files</h3>\n";

    $filesToClean = 0;
    $filesDeleted = 0;

    // Clean up book images
    $bookImagesDir = __DIR__ . '/uploads/books/';
    $thumbsDir = __DIR__ . '/uploads/thumbs/';
    $tempImagesDir = __DIR__ . '/uploads/temp_images/';

    $dirsToClean = [
        $bookImagesDir => 'Book Cover Images',
        $thumbsDir => 'Thumbnail Images',
        $tempImagesDir => 'Temporary Import Images'
    ];

    foreach ($dirsToClean as $dir => $description) {
        if (is_dir($dir)) {
            $files = glob($dir . '*');
            $dirFileCount = count($files);
            $filesToClean += $dirFileCount;

            foreach ($files as $file) {
                if (is_file($file)) {
                    if (unlink($file)) {
                        $filesDeleted++;
                    }
                }
            }

            echo "<div class='success'>✅ Cleaned {$description}: {$dirFileCount} files</div>\n";
        } else {
            echo "<div class='info'>ℹ️ Skipped {$description}: Directory does not exist</div>\n";
        }
    }

    echo "<div class='info'>ℹ️ Total files cleaned: {$filesDeleted} of {$filesToClean}</div>\n";
    echo "</div>\n";

    // PHASE 5: Seed Essential System Data Only
    echo "<div class='step'>\n";
    echo "<h3>🌱 Phase 5: Seeding Essential System Data</h3>\n";

    // Begin transaction for data seeding
    $conn->beginTransaction();

    try {
        // Insert only essential default conditions (required for book management)
        $conditions = [
            ['cond001', 'New', 'Brand new condition', '#22c55e', 1],
            ['cond002', 'Excellent', 'Excellent condition with minimal wear', '#16a34a', 2],
            ['cond003', 'Good', 'Good condition with some wear', '#eab308', 3],
            ['cond004', 'Fair', 'Fair condition with noticeable wear', '#f59e0b', 4],
            ['cond005', 'Poor', 'Poor condition with significant wear', '#ef4444', 5],
            ['cond006', 'Damaged', 'Damaged but still readable', '#dc2626', 6]
        ];

        $stmt = $conn->prepare("INSERT INTO conditions (id, name, description, color, sortOrder) VALUES (?, ?, ?, ?, ?)");
        foreach ($conditions as $condition) {
            $stmt->execute($condition);
        }
        echo "<div class='success'>✅ Inserted " . count($conditions) . " essential book conditions</div>\n";

        // Note: Not inserting sample languages, categories, authors, publishers, or locations
        // These will be added by users as needed
        echo "<div class='info'>ℹ️ Database is now completely empty except for essential conditions and preserved users</div>\n";
        echo "<div class='info'>ℹ️ You can now add languages, categories, authors, and other data as needed</div>\n";

        // Commit the transaction
        $conn->commit();
        echo "<div class='success'>✅ Essential system data seeded successfully</div>\n";

    } catch (Exception $e) {
        $conn->rollback();
        echo "<div class='error'>❌ Error seeding data: " . htmlspecialchars($e->getMessage()) . "</div>\n";
        throw $e;
    }

    echo "</div>\n";

    // PHASE 6: Final Statistics
    echo "<div class='step'>\n";
    echo "<h3>📊 Phase 6: Final Database Statistics</h3>\n";

    $finalStats = [];
    $statTables = ['books', 'authors', 'categories', 'languages', 'publishers', 'members', 'conditions', 'locations', 'users', 'borrowings', 'import_jobs', 'image_import_jobs'];

    foreach ($statTables as $table) {
        try {
            // Check if table exists first
            $tableExists = $conn->query("SHOW TABLES LIKE '{$table}'")->rowCount() > 0;

            if ($tableExists) {
                $count = $conn->query("SELECT COUNT(*) FROM {$table}")->fetchColumn();
                $finalStats[$table] = $count;
            } else {
                $finalStats[$table] = 'N/A';
            }
        } catch (PDOException $e) {
            $finalStats[$table] = 'Error';
        }
    }

    echo "<div class='stats-grid'>\n";
    foreach ($finalStats as $table => $count) {
        echo "<div class='stat-card'>\n";
        echo "    <div class='stat-number'>{$count}</div>\n";
        echo "    <div class='stat-label'>" . ucfirst($table) . "</div>\n";
        echo "</div>\n";
    }
    echo "</div>\n";
    echo "</div>\n";

    // Calculate total cleared records
    $totalCleared = array_sum($clearedCounts);

    // Success completion message
    echo "</div>\n"; // Close progress-container

    echo "<div class='completion-box'>\n";
    echo "<h2>🎉 Database Reset Completed Successfully!</h2>\n";
    echo "<p style='font-size: 1.2em; margin-bottom: 20px; color: #22c55e; font-weight: 600;'>\n";
    echo "    Your Maithili Library database has been completely reset!\n";
    echo "</p>\n";

    echo "<div style='text-align: left; max-width: 500px; margin: 0 auto;'>\n";
    echo "<h4 style='color: #1f2937; margin-bottom: 15px;'>Reset Summary:</h4>\n";
    echo "<ul style='list-style: none; padding: 0;'>\n";
    echo "    <li style='padding: 5px 0; color: #ef4444;'>🗑️ Cleared {$totalCleared} total records</li>\n";
    echo "    <li style='padding: 5px 0; color: #ef4444;'>🗂️ Cleaned {$filesDeleted} uploaded files</li>\n";
    echo "    <li style='padding: 5px 0; color: #22c55e;'>✅ {$finalStats['users']} user accounts preserved</li>\n";
    echo "    <li style='padding: 5px 0; color: #22c55e;'>✅ {$finalStats['conditions']} essential book conditions added</li>\n";
    echo "    <li style='padding: 5px 0; color: #3b82f6;'>📝 {$finalStats['languages']} languages (ready for your data)</li>\n";
    echo "    <li style='padding: 5px 0; color: #3b82f6;'>📝 {$finalStats['categories']} categories (ready for your data)</li>\n";
    echo "    <li style='padding: 5px 0; color: #3b82f6;'>📝 {$finalStats['authors']} authors (ready for your data)</li>\n";
    echo "    <li style='padding: 5px 0; color: #3b82f6;'>📝 {$finalStats['publishers']} publishers (ready for your data)</li>\n";
    echo "    <li style='padding: 5px 0; color: #3b82f6;'>📝 {$finalStats['locations']} locations (ready for your data)</li>\n";
    echo "    <li style='padding: 5px 0; color: #8b5cf6;'>📥 {$finalStats['import_jobs']} book import jobs</li>\n";
    echo "    <li style='padding: 5px 0; color: #8b5cf6;'>🖼️ {$finalStats['image_import_jobs']} image import jobs</li>\n";
    echo "</ul>\n";
    echo "</div>\n";

    echo "<p style='margin-top: 20px; color: #666; font-size: 0.95em;'>\n";
    echo "    The library is now completely reset with all data cleared (except users). All book images, import history, and uploaded files have been removed. You can start fresh with new data entry.\n";
    echo "</p>\n";
    echo "</div>\n";

} catch (Exception $e) {
    echo "</div>\n"; // Close progress-container if open

    echo "<div class='step'>\n";
    echo "<h3>❌ Reset Failed</h3>\n";
    echo "<div class='error'>\n";
    echo "    <p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "    <p style='margin-top: 10px;'><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>\n";
    echo "    <p><strong>Line:</strong> " . $e->getLine() . "</p>\n";
    echo "</div>\n";
    echo "<div class='info'>\n";
    echo "    <p>The database may be in a partial state. You may need to run the reset again or restore from backup.</p>\n";
    echo "</div>\n";
    echo "</div>\n";
}
?>
<?php endif; ?>

        <div class="back-links">
            <a href="index.php" class="btn btn-secondary">🏠 Go to Library</a>
            <a href="admin/" class="btn btn-secondary">⚙️ Admin Panel</a>
            <?php if ($confirmed): ?>
            <a href="database_reset.php" class="btn btn-secondary">🔄 Reset Again</a>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
