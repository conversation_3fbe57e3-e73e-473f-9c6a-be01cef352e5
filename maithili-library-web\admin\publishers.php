<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Publishers Management
 */

require_once '../config/config.php';
require_once '../includes/Database.php';
require_once '../includes/functions.php';

// Check if user is logged in
require_admin();

// Initialize database
$db = new Database();

// Handle actions
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? null;
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
            case 'edit':
                $name = trim($_POST['name'] ?? '');
                $nameNepali = trim($_POST['nameNepali'] ?? '') ?: null;
                $address = trim($_POST['address'] ?? '') ?: null;
                $phone = trim($_POST['phone'] ?? '') ?: null;
                $email = trim($_POST['email'] ?? '') ?: null;
                $website = trim($_POST['website'] ?? '') ?: null;
                $country = trim($_POST['country'] ?? '') ?: null;
                $description = trim($_POST['description'] ?? '') ?: null;
                $isActive = isset($_POST['isActive']) ? 1 : 0;
                
                // Validation
                if (empty($name)) {
                    $error = 'Publisher name is required';
                } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $error = 'Invalid email address';
                } elseif (!empty($website) && !filter_var($website, FILTER_VALIDATE_URL)) {
                    $error = 'Invalid website URL';
                } else {
                    try {
                        if ($_POST['action'] === 'add') {
                            // Check if publisher already exists
                            $existing = $db->fetch("SELECT id FROM publishers WHERE name = ? AND isDeleted = 0", [$name]);
                            if ($existing) {
                                $error = 'Publisher with this name already exists';
                            } else {
                                $publisher_id = 'pub' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
                                $data = [
                                    'id' => $publisher_id,
                                    'name' => $name,
                                    'nameNepali' => $nameNepali,
                                    'address' => $address,
                                    'phone' => $phone,
                                    'email' => $email,
                                    'website' => $website,
                                    'country' => $country,
                                    'description' => $description,
                                    'isActive' => $isActive,
                                    'createdAt' => date('Y-m-d H:i:s'),
                                    'updatedAt' => date('Y-m-d H:i:s')
                                ];
                                $db->insert('publishers', $data);
                                $message = 'Publisher added successfully';
                                log_activity("Added publisher: $name", 'INFO');
                                $action = 'list';
                            }
                        } else {
                            // Edit existing publisher
                            $edit_id = $_POST['id'] ?? '';
                            if ($edit_id) {
                                // Check if name conflicts with other publishers
                                $existing = $db->fetch("SELECT id FROM publishers WHERE name = ? AND id != ? AND isDeleted = 0", [$name, $edit_id]);
                                if ($existing) {
                                    $error = 'Publisher with this name already exists';
                                } else {
                                    $data = [
                                        'name' => $name,
                                        'nameNepali' => $nameNepali,
                                        'address' => $address,
                                        'phone' => $phone,
                                        'email' => $email,
                                        'website' => $website,
                                        'country' => $country,
                                        'description' => $description,
                                        'isActive' => $isActive,
                                        'updatedAt' => date('Y-m-d H:i:s')
                                    ];
                                    $db->update('publishers', $data, 'id = ?', [$edit_id]);
                                    $message = 'Publisher updated successfully';
                                    log_activity("Updated publisher: $name", 'INFO');
                                    $action = 'list';
                                }
                            }
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                        log_activity("Publisher save error: " . $e->getMessage(), 'ERROR');
                    }
                }
                break;
                
            case 'delete':
                $delete_id = $_POST['id'] ?? '';
                if ($delete_id) {
                    try {
                        // Check if publisher has books
                        $book_count = $db->fetchColumn("SELECT COUNT(*) FROM books WHERE publisherId = ? AND isDeleted = 0", [$delete_id]);
                        if ($book_count > 0) {
                            $error = "Cannot delete publisher. $book_count books are associated with this publisher.";
                        } else {
                            $publisher_name = $db->fetchColumn("SELECT name FROM publishers WHERE id = ?", [$delete_id]);
                            $db->delete('publishers', 'id = ?', [$delete_id]);
                            $message = 'Publisher deleted successfully';
                            log_activity("Deleted publisher: $publisher_name", 'INFO');
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                        log_activity("Publisher delete error: " . $e->getMessage(), 'ERROR');
                    }
                }
                break;
        }
    }
}

// Handle AJAX delete request
if (isset($_GET['ajax_delete']) && $_GET['ajax_delete'] === '1' && $id) {
    header('Content-Type: application/json');
    try {
        // Check if publisher has books
        $book_count = $db->fetchColumn("SELECT COUNT(*) FROM books WHERE publisherId = ? AND isDeleted = 0", [$id]);
        if ($book_count > 0) {
            echo json_encode(['success' => false, 'message' => "Cannot delete publisher. $book_count books are associated with this publisher."]);
        } else {
            $publisher_name = $db->fetchColumn("SELECT name FROM publishers WHERE id = ?", [$id]);
            $db->delete('publishers', 'id = ?', [$id]);
            log_activity("Deleted publisher: $publisher_name", 'INFO');
            echo json_encode(['success' => true, 'message' => 'Publisher deleted successfully']);
        }
    } catch (Exception $e) {
        log_activity("Publisher delete error: " . $e->getMessage(), 'ERROR');
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
    exit;
}

// Get data for edit form
$publisher_data = null;
if ($action === 'edit' && $id) {
    $publisher_data = $db->fetch("SELECT * FROM publishers WHERE id = ? AND isDeleted = 0", [$id]);
    if (!$publisher_data) {
        $error = 'Publisher not found';
        $action = 'list';
    }
}

// Get publishers list for list view
$publishers = [];
$total_publishers = 0;
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = ADMIN_ITEMS_PER_PAGE;
$search = trim($_GET['search'] ?? '');
$status_filter = $_GET['status'] ?? '';

if ($action === 'list') {
    try {
        $where_conditions = ['p.isDeleted = 0'];
        $params = [];

        if (!empty($search)) {
            $where_conditions[] = '(p.name LIKE ? OR p.nameNepali LIKE ? OR p.country LIKE ? OR p.description LIKE ?)';
            $search_param = "%$search%";
            $params[] = $search_param;
            $params[] = $search_param;
            $params[] = $search_param;
            $params[] = $search_param;
        }

        if ($status_filter === 'active') {
            $where_conditions[] = 'p.isActive = 1';
        } elseif ($status_filter === 'inactive') {
            $where_conditions[] = 'p.isActive = 0';
        }

        $where_clause = implode(' AND ', $where_conditions);

        // Get total count
        $total_publishers = $db->fetchColumn("
            SELECT COUNT(*)
            FROM publishers p
            WHERE $where_clause
        ", $params);

        // Get publishers with book count
        $offset = ($page - 1) * $per_page;
        $publishers = $db->fetchAll("
            SELECT p.*,
                   COUNT(b.id) as bookCount
            FROM publishers p
            LEFT JOIN books b ON p.id = b.publisherId AND b.isDeleted = 0
            WHERE $where_clause
            GROUP BY p.id
            ORDER BY p.name ASC
            LIMIT $per_page OFFSET $offset
        ", $params);
        
    } catch (Exception $e) {
        $error = 'Database error: ' . $e->getMessage();
        log_activity("Publishers list error: " . $e->getMessage(), 'ERROR');
    }
}

$page_title = 'Publishers - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="<?= asset_url('css/style.css') ?>">
    <link rel="stylesheet" href="<?= asset_url('css/admin.css') ?>">
    
    <!-- Favicon and App Icons -->
    <?= generate_favicon_tags() ?>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <button class="mobile-close-btn" id="mobile-close-btn">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <div class="sidebar-logo">
                    <img src="<?= icon_url(48) ?>" alt="<?= APP_NAME ?> Icon" class="sidebar-logo-image">
                </div>
                <h2 class="sidebar-title">Admin Panel</h2>
                <p class="sidebar-subtitle">मैथिली विकास कोष</p>
            </div>

            <!-- Mobile User Section -->
            <div class="mobile-user-section">
                <div class="mobile-user-info">
                    <div class="mobile-user-avatar">
                        <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                    </div>
                    <div class="mobile-user-details">
                        <span class="mobile-user-name"><?= htmlspecialchars($_SESSION['admin_name']) ?></span>
                        <span class="mobile-user-role">Administrator</span>
                    </div>
                </div>
                <a href="logout.php" class="mobile-logout-btn">
                    <span class="logout-icon">🚪</span>
                    Logout
                </a>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <div class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Dashboard
                        </a>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Book Management</div>
                    <div class="nav-item">
                        <a href="books.php" class="nav-link">
                            <span class="nav-icon">📚</span>
                            Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="books.php?action=add" class="nav-link">
                            <span class="nav-icon">➕</span>
                            Add Book
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-books.php" class="nav-link">
                            <span class="nav-icon">📥</span>
                            Import Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-images.php" class="nav-link">
                            <span class="nav-icon">🖼️</span>
                            Import Images
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Catalog Management</div>
                    <div class="nav-item">
                        <a href="authors.php" class="nav-link">
                            <span class="nav-icon">✍️</span>
                            Authors
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="categories.php" class="nav-link">
                            <span class="nav-icon">🏷️</span>
                            Categories
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="publishers.php" class="nav-link active">
                            <span class="nav-icon">🏢</span>
                            Publishers
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="languages.php" class="nav-link">
                            <span class="nav-icon">🌐</span>
                            Languages
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="subjects.php" class="nav-link">
                            <span class="nav-icon">📖</span>
                            Subjects
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="series.php" class="nav-link">
                            <span class="nav-icon">📑</span>
                            Book Series
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="locations.php" class="nav-link">
                            <span class="nav-icon">📍</span>
                            Locations
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="conditions.php" class="nav-link">
                            <span class="nav-icon">🔧</span>
                            Conditions
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="sources.php" class="nav-link">
                            <span class="nav-icon">🎁</span>
                            Sources
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Reports & Analytics</div>
                    <div class="nav-item">
                        <a href="reports.php" class="nav-link">
                            <span class="nav-icon">📈</span>
                            Collection Reports
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="analytics.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Analytics
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <div class="nav-item">
                        <a href="users.php" class="nav-link">
                            <span class="nav-icon">👤</span>
                            Admin Users
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="settings.php" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            Settings
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="backup.php" class="nav-link">
                            <span class="nav-icon">💾</span>
                            Backup & Export
                        </a>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <header class="admin-header">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="header-title">
                            <span class="title-icon">🏢</span>
                            Publishers Management
                        </h1>
                        <p class="header-subtitle">Manage book publishers and their information</p>
                    </div>
                    <div class="header-actions">
                        <div class="user-info">
                            <span class="welcome-text">Welcome, <?= htmlspecialchars($_SESSION['admin_name']) ?>!</span>
                            <div class="user-menu">
                                <div class="user-avatar">
                                    <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                                </div>
                            </div>
                        </div>
                        <a href="logout.php" class="btn btn-secondary btn-sm">
                            <span style="margin-right: 0.5rem;">🚪</span>
                            Logout
                        </a>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Breadcrumb -->
                <nav class="breadcrumb">
                    <a href="dashboard.php" class="breadcrumb-item">Dashboard</a>
                    <span class="breadcrumb-separator">›</span>
                    <span class="breadcrumb-item active">Publishers</span>
                </nav>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <span class="alert-icon">✅</span>
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <span class="alert-icon">❌</span>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <?php if ($action === 'list'): ?>
                    <!-- Publishers List -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <h2 class="page-title">Publishers</h2>
                            <p class="page-subtitle">Manage book publishers and their information</p>
                        </div>
                        <div class="page-header-actions">
                            <a href="?action=add" class="btn btn-primary">
                                <span style="margin-right: 0.5rem;">➕</span>
                                Add Publisher
                            </a>
                        </div>
                    </div>

                    <!-- Search and Filters -->
                    <div class="card">
                        <div class="card-body">
                            <form method="GET" class="search-form">
                                <div class="search-form-row">
                                    <div class="search-input-group">
                                        <input type="text" name="search" value="<?= htmlspecialchars($search) ?>"
                                               placeholder="Search publishers by name, country, or description..." class="input">
                                        <select name="status" class="input">
                                            <option value="">All Status</option>
                                            <option value="active" <?= $status_filter === 'active' ? 'selected' : '' ?>>Active</option>
                                            <option value="inactive" <?= $status_filter === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                        </select>
                                        <button type="submit" class="btn btn-secondary">
                                            <span style="margin-right: 0.5rem;">🔍</span>
                                            Search
                                        </button>
                                    </div>
                                    <?php if (!empty($search) || !empty($status_filter)): ?>
                                        <a href="?" class="btn btn-outline">Clear</a>
                                    <?php endif; ?>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Publishers Table -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                Publishers List
                                <?php if (!empty($search)): ?>
                                    <span class="text-muted">(Search: "<?= htmlspecialchars($search) ?>")</span>
                                <?php endif; ?>
                                <?php if (!empty($status_filter)): ?>
                                    <span class="text-muted">(Status: <?= ucfirst($status_filter) ?>)</span>
                                <?php endif; ?>
                            </h3>
                            <div class="card-header-actions">
                                <span class="text-muted">Total: <?= number_format($total_publishers) ?> publishers</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($publishers)): ?>
                                <div class="empty-state">
                                    <div class="empty-state-icon">🏢</div>
                                    <h3 class="empty-state-title">No Publishers Found</h3>
                                    <p class="empty-state-description">
                                        <?php if (!empty($search) || !empty($status_filter)): ?>
                                            No publishers match your search criteria. Try adjusting your search terms or filters.
                                        <?php else: ?>
                                            Start by adding your first publisher to the library system.
                                        <?php endif; ?>
                                    </p>
                                    <div class="empty-state-actions">
                                        <?php if (!empty($search) || !empty($status_filter)): ?>
                                            <a href="?" class="btn btn-secondary">Clear Filters</a>
                                        <?php endif; ?>
                                        <a href="?action=add" class="btn btn-primary">Add First Publisher</a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Contact</th>
                                                <th>Country</th>
                                                <th>Books</th>
                                                <th>Status</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($publishers as $publisher): ?>
                                                <tr>
                                                    <td>
                                                        <div class="publisher-name">
                                                            <strong><?= htmlspecialchars($publisher['name']) ?></strong>
                                                            <?php if (!empty($publisher['nameNepali'])): ?>
                                                                <div class="publisher-name-nepali">
                                                                    <span class="nepali-text"><?= htmlspecialchars($publisher['nameNepali']) ?></span>
                                                                </div>
                                                            <?php endif; ?>
                                                            <?php if (!empty($publisher['description'])): ?>
                                                                <div class="publisher-description">
                                                                    <?= htmlspecialchars(substr($publisher['description'], 0, 80)) ?>
                                                                    <?php if (strlen($publisher['description']) > 80): ?>...<?php endif; ?>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="publisher-contact">
                                                            <?php if (!empty($publisher['email'])): ?>
                                                                <div class="contact-item">
                                                                    📧 <a href="mailto:<?= htmlspecialchars($publisher['email']) ?>"><?= htmlspecialchars($publisher['email']) ?></a>
                                                                </div>
                                                            <?php endif; ?>
                                                            <?php if (!empty($publisher['phone'])): ?>
                                                                <div class="contact-item">
                                                                    📞 <?= htmlspecialchars($publisher['phone']) ?>
                                                                </div>
                                                            <?php endif; ?>
                                                            <?php if (!empty($publisher['website'])): ?>
                                                                <div class="contact-item">
                                                                    🌐 <a href="<?= htmlspecialchars($publisher['website']) ?>" target="_blank" rel="noopener">Website</a>
                                                                </div>
                                                            <?php endif; ?>
                                                            <?php if (empty($publisher['email']) && empty($publisher['phone']) && empty($publisher['website'])): ?>
                                                                <span class="text-muted">—</span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <?php if (!empty($publisher['country'])): ?>
                                                            <?= htmlspecialchars($publisher['country']) ?>
                                                        <?php else: ?>
                                                            <span class="text-muted">—</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-info"><?= number_format($publisher['bookCount']) ?> books</span>
                                                    </td>
                                                    <td>
                                                        <?php if ($publisher['isActive']): ?>
                                                            <span class="badge badge-success">Active</span>
                                                        <?php else: ?>
                                                            <span class="badge badge-warning">Inactive</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="text-muted"><?= date('M j, Y', strtotime($publisher['createdAt'])) ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <a href="?action=edit&id=<?= urlencode($publisher['id']) ?>"
                                                               class="btn btn-sm btn-outline" title="Edit Publisher">
                                                                ✏️
                                                            </a>
                                                            <?php if ($publisher['bookCount'] == 0): ?>
                                                                <button type="button"
                                                                        class="btn btn-sm btn-danger delete-publisher"
                                                                        data-id="<?= htmlspecialchars($publisher['id']) ?>"
                                                                        data-name="<?= htmlspecialchars($publisher['name']) ?>"
                                                                        title="Delete Publisher">
                                                                    🗑️
                                                                </button>
                                                            <?php else: ?>
                                                                <button type="button"
                                                                        class="btn btn-sm btn-outline"
                                                                        disabled
                                                                        title="Cannot delete - has <?= $publisher['bookCount'] ?> books">
                                                                    🔒
                                                                </button>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <?php if ($total_publishers > $per_page): ?>
                                    <div class="pagination-wrapper">
                                        <?php
                                        $total_pages = ceil($total_publishers / $per_page);
                                        $query_params = $_GET;
                                        ?>
                                        <div class="pagination">
                                            <?php if ($page > 1): ?>
                                                <?php $query_params['page'] = $page - 1; ?>
                                                <a href="?<?= http_build_query($query_params) ?>" class="pagination-btn">‹ Previous</a>
                                            <?php endif; ?>

                                            <?php
                                            $start_page = max(1, $page - 2);
                                            $end_page = min($total_pages, $page + 2);

                                            for ($i = $start_page; $i <= $end_page; $i++):
                                                $query_params['page'] = $i;
                                            ?>
                                                <a href="?<?= http_build_query($query_params) ?>"
                                                   class="pagination-btn <?= $i === $page ? 'active' : '' ?>">
                                                    <?= $i ?>
                                                </a>
                                            <?php endfor; ?>

                                            <?php if ($page < $total_pages): ?>
                                                <?php $query_params['page'] = $page + 1; ?>
                                                <a href="?<?= http_build_query($query_params) ?>" class="pagination-btn">Next ›</a>
                                            <?php endif; ?>
                                        </div>
                                        <div class="pagination-info">
                                            Showing <?= number_format(($page - 1) * $per_page + 1) ?> to
                                            <?= number_format(min($page * $per_page, $total_publishers)) ?> of
                                            <?= number_format($total_publishers) ?> publishers
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                <?php elseif ($action === 'add' || $action === 'edit'): ?>
                    <!-- Add/Edit Publisher Form -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <h2 class="page-title"><?= $action === 'add' ? 'Add New Publisher' : 'Edit Publisher' ?></h2>
                            <p class="page-subtitle">
                                <?= $action === 'add' ? 'Enter publisher information to add to the library system' : 'Update publisher information' ?>
                            </p>
                        </div>
                        <div class="page-header-actions">
                            <a href="?" class="btn btn-secondary">
                                <span style="margin-right: 0.5rem;">←</span>
                                Back to Publishers
                            </a>
                        </div>
                    </div>

                    <form method="POST" class="publisher-form">
                        <input type="hidden" name="action" value="<?= $action ?>">
                        <?php if ($action === 'edit' && $publisher_data): ?>
                            <input type="hidden" name="id" value="<?= htmlspecialchars($publisher_data['id']) ?>">
                        <?php endif; ?>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Publisher Information</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="name" class="label required">Publisher Name</label>
                                        <input type="text"
                                               id="name"
                                               name="name"
                                               class="input"
                                               placeholder="Enter publisher name"
                                               value="<?= htmlspecialchars($publisher_data['name'] ?? '') ?>"
                                               required>
                                        <div class="help-text">The primary name of the publisher (required)</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="nameNepali" class="label">Nepali/Maithili Name</label>
                                        <input type="text"
                                               id="nameNepali"
                                               name="nameNepali"
                                               class="input nepali-input"
                                               placeholder="प्रकाशकको नाम"
                                               value="<?= htmlspecialchars($publisher_data['nameNepali'] ?? '') ?>">
                                        <div class="help-text">Publisher name in Nepali/Maithili script (optional)</div>
                                    </div>
                                </div>

                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="email" class="label">Email</label>
                                        <input type="email"
                                               id="email"
                                               name="email"
                                               class="input"
                                               placeholder="<EMAIL>"
                                               value="<?= htmlspecialchars($publisher_data['email'] ?? '') ?>">
                                        <div class="help-text">Publisher's email address (optional)</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="phone" class="label">Phone</label>
                                        <input type="text"
                                               id="phone"
                                               name="phone"
                                               class="input"
                                               placeholder="Phone number"
                                               value="<?= htmlspecialchars($publisher_data['phone'] ?? '') ?>">
                                        <div class="help-text">Publisher's phone number (optional)</div>
                                    </div>
                                </div>

                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="website" class="label">Website</label>
                                        <input type="url"
                                               id="website"
                                               name="website"
                                               class="input"
                                               placeholder="https://publisher-website.com"
                                               value="<?= htmlspecialchars($publisher_data['website'] ?? '') ?>">
                                        <div class="help-text">Publisher's website URL (optional)</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="country" class="label">Country</label>
                                        <input type="text"
                                               id="country"
                                               name="country"
                                               class="input"
                                               placeholder="Country name"
                                               value="<?= htmlspecialchars($publisher_data['country'] ?? '') ?>">
                                        <div class="help-text">Publisher's country (optional)</div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="address" class="label">Address</label>
                                    <textarea id="address"
                                              name="address"
                                              class="textarea"
                                              rows="3"
                                              placeholder="Enter publisher address..."><?= htmlspecialchars($publisher_data['address'] ?? '') ?></textarea>
                                    <div class="help-text">Publisher's full address (optional)</div>
                                </div>

                                <div class="form-group">
                                    <label for="description" class="label">Description</label>
                                    <textarea id="description"
                                              name="description"
                                              class="textarea"
                                              rows="4"
                                              placeholder="Enter publisher description..."><?= htmlspecialchars($publisher_data['description'] ?? '') ?></textarea>
                                    <div class="help-text">Brief description of the publisher (optional)</div>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox"
                                               name="isActive"
                                               value="1"
                                               <?= ($publisher_data['isActive'] ?? 1) ? 'checked' : '' ?>>
                                        <span class="checkbox-text">Active Publisher</span>
                                    </label>
                                    <div class="help-text">Uncheck to deactivate this publisher</div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <span style="margin-right: 0.5rem;">💾</span>
                                <?= $action === 'add' ? 'Add Publisher' : 'Update Publisher' ?>
                            </button>
                            <a href="?" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Confirm Delete</h3>
                <button type="button" class="modal-close" onclick="closeDeleteModal()">×</button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the publisher "<span id="deletePublisherName"></span>"?</p>
                <p class="text-warning">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeDeleteModal()">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">Delete Publisher</button>
            </div>
        </div>
    </div>

    <script>
        // Mobile Menu Functionality
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');

            sidebar.classList.toggle('open');
            overlay.classList.toggle('active');

            if (sidebar.classList.contains('open')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        function closeMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');

            sidebar.classList.remove('open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        // Delete functionality
        let deletePublisherId = null;

        function openDeleteModal(id, name) {
            deletePublisherId = id;
            document.getElementById('deletePublisherName').textContent = name;
            document.getElementById('deleteModal').style.display = 'flex';
        }

        function closeDeleteModal() {
            deletePublisherId = null;
            document.getElementById('deleteModal').style.display = 'none';
        }

        function confirmDelete() {
            if (deletePublisherId) {
                // Send AJAX request to delete
                fetch(`?ajax_delete=1&id=${encodeURIComponent(deletePublisherId)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Reload page to show updated list
                            window.location.reload();
                        } else {
                            alert('Error: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while deleting the publisher.');
                    });
            }
            closeDeleteModal();
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Mobile menu toggle button event listener
            const mobileToggle = document.getElementById('mobile-menu-toggle');
            if (mobileToggle) {
                mobileToggle.addEventListener('click', toggleMobileMenu);
            }

            // Mobile overlay click event listener
            const mobileOverlay = document.getElementById('mobile-overlay');
            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', closeMobileMenu);
            }

            // Mobile close button event listener
            const mobileCloseBtn = document.getElementById('mobile-close-btn');
            if (mobileCloseBtn) {
                mobileCloseBtn.addEventListener('click', closeMobileMenu);
            }

            // Delete button event listeners
            document.querySelectorAll('.delete-publisher').forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const name = this.getAttribute('data-name');
                    openDeleteModal(id, name);
                });
            });

            // Close mobile menu when clicking on nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth <= 1023) {
                        closeMobileMenu();
                    }
                });
            });

            // Handle escape key to close modal
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    closeDeleteModal();
                    closeMobileMenu();
                }
            });

            console.log('Publishers page loaded successfully');
        });

        // Close mobile menu on window resize if screen becomes large
        window.addEventListener('resize', () => {
            if (window.innerWidth > 1023) {
                closeMobileMenu();
            }
        });
    </script>
</body>
</html>
