<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Conditions Management
 */

require_once '../config/config.php';
require_once '../includes/Database.php';
require_once '../includes/functions.php';

// Check if user is logged in
require_admin();

// Initialize database
$db = new Database();

// Handle actions
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? null;
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
            case 'edit':
                $name = trim($_POST['name'] ?? '');
                $description = trim($_POST['description'] ?? '') ?: '';
                $color = trim($_POST['color'] ?? '') ?: '#6b7280';
                $sortOrder = intval($_POST['sortOrder'] ?? 0) ?: 0;
                $isActive = isset($_POST['isActive']) ? 1 : 0;
                
                // Validation
                if (empty($name)) {
                    $error = 'Condition name is required';
                } elseif (!empty($color) && !preg_match('/^#[0-9A-Fa-f]{6}$/', $color)) {
                    $error = 'Color must be a valid hex color code (e.g., #FF0000)';
                } else {
                    try {
                        if ($_POST['action'] === 'add') {
                            // Check if condition already exists
                            $existing = $db->fetch("SELECT id FROM conditions WHERE name = ?", [$name]);
                            if ($existing) {
                                $error = 'Condition with this name already exists';
                            } else {
                                $condition_id = 'cond' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
                                $data = [
                                    'id' => $condition_id,
                                    'name' => $name,
                                    'description' => $description,
                                    'color' => $color,
                                    'sortOrder' => $sortOrder,
                                    'isActive' => $isActive
                                ];
                                $db->insert('conditions', $data);
                                $message = 'Condition added successfully';
                                // log_activity("Added condition: $name", 'INFO');
                                $action = 'list';
                            }
                        } else {
                            // Edit existing condition
                            $edit_id = $_POST['id'] ?? '';
                            if ($edit_id) {
                                // Check if name conflicts with other conditions
                                $existing = $db->fetch("SELECT id FROM conditions WHERE name = ? AND id != ?", [$name, $edit_id]);
                                if ($existing) {
                                    $error = 'Condition with this name already exists';
                                } else {
                                    $data = [
                                        'name' => $name,
                                        'description' => $description,
                                        'color' => $color,
                                        'sortOrder' => $sortOrder,
                                        'isActive' => $isActive
                                    ];
                                    $db->update('conditions', $data, 'id = ?', [$edit_id]);
                                    $message = 'Condition updated successfully';
                                    // log_activity("Updated condition: $name", 'INFO');
                                    $action = 'list';
                                }
                            }
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                        // log_activity("Condition save error: " . $e->getMessage(), 'ERROR');
                    }
                }
                break;
                
            case 'delete':
                $delete_id = $_POST['id'] ?? '';
                if ($delete_id) {
                    try {
                        // Check if condition has books
                        $book_count = $db->fetchColumn("SELECT COUNT(*) FROM books WHERE conditionId = ? AND isDeleted = 0", [$delete_id]);
                        if ($book_count > 0) {
                            $error = "Cannot delete condition. $book_count books are associated with this condition.";
                        } else {
                            $condition_name = $db->fetchColumn("SELECT name FROM conditions WHERE id = ?", [$delete_id]);
                            $db->query("DELETE FROM conditions WHERE id = ?", [$delete_id]);
                            $message = 'Condition deleted successfully';
                            // log_activity("Deleted condition: $condition_name", 'INFO');
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                        // log_activity("Condition delete error: " . $e->getMessage(), 'ERROR');
                    }
                }
                break;
        }
    }
}

// Handle AJAX delete request
if (isset($_GET['ajax_delete']) && $_GET['ajax_delete'] === '1' && $id) {
    header('Content-Type: application/json');
    try {
        // Check if condition has books
        $book_count = $db->fetchColumn("SELECT COUNT(*) FROM books WHERE conditionId = ? AND isDeleted = 0", [$id]);
        if ($book_count > 0) {
            echo json_encode(['success' => false, 'message' => "Cannot delete condition. $book_count books are associated with this condition."]);
        } else {
            $condition_name = $db->fetchColumn("SELECT name FROM conditions WHERE id = ?", [$id]);
            $db->query("DELETE FROM conditions WHERE id = ?", [$id]);
            // log_activity("Deleted condition: $condition_name", 'INFO');
            echo json_encode(['success' => true, 'message' => 'Condition deleted successfully']);
        }
    } catch (Exception $e) {
        log_activity("Condition delete error: " . $e->getMessage(), 'ERROR');
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
    exit;
}

// Get data for edit form
$condition_data = null;
if ($action === 'edit' && $id) {
    $condition_data = $db->fetch("SELECT * FROM conditions WHERE id = ?", [$id]);
    if (!$condition_data) {
        $error = 'Condition not found';
        $action = 'list';
    }
}

// Get conditions list for list view
$conditions = [];
$total_conditions = 0;
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = ADMIN_ITEMS_PER_PAGE;
$search = trim($_GET['search'] ?? '');
$status_filter = $_GET['status'] ?? '';

if ($action === 'list') {
    try {
        $where_conditions = ['1=1'];
        $params = [];
        
        if (!empty($search)) {
            $where_conditions[] = '(c.name LIKE ? OR c.description LIKE ?)';
            $search_param = "%$search%";
            $params[] = $search_param;
            $params[] = $search_param;
        }
        
        if ($status_filter === 'active') {
            $where_conditions[] = 'c.isActive = 1';
        } elseif ($status_filter === 'inactive') {
            $where_conditions[] = 'c.isActive = 0';
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        // Get total count
        $total_conditions = $db->fetchColumn("
            SELECT COUNT(*)
            FROM conditions c
            WHERE $where_clause
        ", $params);
        
        // Get conditions with book count
        $offset = ($page - 1) * $per_page;
        $conditions = $db->fetchAll("
            SELECT c.*, 
                   COUNT(b.id) as bookCount
            FROM conditions c
            LEFT JOIN books b ON c.id = b.conditionId AND b.isDeleted = 0
            WHERE $where_clause
            GROUP BY c.id
            ORDER BY c.sortOrder ASC, c.name ASC
            LIMIT $per_page OFFSET $offset
        ", $params);
        
    } catch (Exception $e) {
        $error = 'Database error: ' . $e->getMessage();
        log_activity("Conditions list error: " . $e->getMessage(), 'ERROR');
    }
}

$page_title = 'Conditions - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="<?= asset_url('css/style.css') ?>">
    <link rel="stylesheet" href="<?= asset_url('css/admin.css') ?>">
    
    <!-- Favicon and App Icons -->
    <?= generate_favicon_tags() ?>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <button class="mobile-close-btn" id="mobile-close-btn">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <div class="sidebar-logo">
                    <img src="<?= icon_url(48) ?>" alt="<?= APP_NAME ?> Icon" class="sidebar-logo-image">
                </div>
                <h2 class="sidebar-title">Admin Panel</h2>
                <p class="sidebar-subtitle">मैथिली विकास कोष</p>
            </div>

            <!-- Mobile User Section -->
            <div class="mobile-user-section">
                <div class="mobile-user-info">
                    <div class="mobile-user-avatar">
                        <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                    </div>
                    <div class="mobile-user-details">
                        <span class="mobile-user-name"><?= htmlspecialchars($_SESSION['admin_name']) ?></span>
                        <span class="mobile-user-role">Administrator</span>
                    </div>
                </div>
                <a href="logout.php" class="mobile-logout-btn">
                    <span class="logout-icon">🚪</span>
                    Logout
                </a>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <div class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Dashboard
                        </a>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Book Management</div>
                    <div class="nav-item">
                        <a href="books.php" class="nav-link">
                            <span class="nav-icon">📚</span>
                            Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="books.php?action=add" class="nav-link">
                            <span class="nav-icon">➕</span>
                            Add Book
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-books.php" class="nav-link">
                            <span class="nav-icon">📥</span>
                            Import Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-images.php" class="nav-link">
                            <span class="nav-icon">🖼️</span>
                            Import Images
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Catalog Management</div>
                    <div class="nav-item">
                        <a href="authors.php" class="nav-link">
                            <span class="nav-icon">✍️</span>
                            Authors
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="categories.php" class="nav-link">
                            <span class="nav-icon">🏷️</span>
                            Categories
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="publishers.php" class="nav-link">
                            <span class="nav-icon">🏢</span>
                            Publishers
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="languages.php" class="nav-link">
                            <span class="nav-icon">🌐</span>
                            Languages
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="subjects.php" class="nav-link">
                            <span class="nav-icon">📖</span>
                            Subjects
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="series.php" class="nav-link">
                            <span class="nav-icon">📑</span>
                            Book Series
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="locations.php" class="nav-link">
                            <span class="nav-icon">📍</span>
                            Locations
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="conditions.php" class="nav-link active">
                            <span class="nav-icon">🔧</span>
                            Conditions
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="sources.php" class="nav-link">
                            <span class="nav-icon">🎁</span>
                            Sources
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Reports & Analytics</div>
                    <div class="nav-item">
                        <a href="reports.php" class="nav-link">
                            <span class="nav-icon">📈</span>
                            Collection Reports
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="analytics.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Analytics
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <div class="nav-item">
                        <a href="users.php" class="nav-link">
                            <span class="nav-icon">👤</span>
                            Admin Users
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="settings.php" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            Settings
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="backup.php" class="nav-link">
                            <span class="nav-icon">💾</span>
                            Backup & Export
                        </a>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>

            <!-- Header -->
            <header class="admin-header">
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="header-title">
                            <span class="title-icon">🔧</span>
                            Conditions Management
                        </h1>
                        <p class="header-subtitle">Manage book condition categories and quality ratings</p>
                    </div>
                    <div class="header-actions">
                        <div class="user-info">
                            <span class="welcome-text">Welcome, <?= htmlspecialchars($_SESSION['admin_name']) ?>!</span>
                            <div class="user-menu">
                                <div class="user-avatar">
                                    <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                                </div>
                            </div>
                        </div>
                        <a href="logout.php" class="btn btn-secondary btn-sm">
                            <span style="margin-right: 0.5rem;">🚪</span>
                            Logout
                        </a>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Breadcrumb -->
                <nav class="breadcrumb">
                    <a href="dashboard.php" class="breadcrumb-item">Dashboard</a>
                    <span class="breadcrumb-separator">›</span>
                    <span class="breadcrumb-item active">Conditions</span>
                </nav>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <span class="alert-icon">✅</span>
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <span class="alert-icon">❌</span>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <?php if ($action === 'list'): ?>
                    <!-- Conditions List -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <h2 class="page-title">Conditions</h2>
                            <p class="page-subtitle">Manage book condition categories and quality ratings</p>
                        </div>
                        <div class="page-header-actions">
                            <a href="?action=add" class="btn btn-primary">
                                <span style="margin-right: 0.5rem;">➕</span>
                                Add Condition
                            </a>
                        </div>
                    </div>

                    <!-- Search and Filters -->
                    <div class="card">
                        <div class="card-body">
                            <form method="GET" class="search-form">
                                <div class="search-form-row">
                                    <div class="search-input-group">
                                        <input type="text" name="search" value="<?= htmlspecialchars($search) ?>"
                                               placeholder="Search conditions by name or description..." class="input">
                                        <button type="submit" class="btn btn-secondary">
                                            <span style="margin-right: 0.5rem;">🔍</span>
                                            Search
                                        </button>
                                    </div>
                                    <?php if (!empty($search)): ?>
                                        <a href="?" class="btn btn-outline">Clear</a>
                                    <?php endif; ?>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Conditions Table -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                Conditions List
                                <?php if (!empty($search)): ?>
                                    <span class="text-muted">(Search: "<?= htmlspecialchars($search) ?>")</span>
                                <?php endif; ?>
                            </h3>
                            <div class="card-header-actions">
                                <span class="text-muted">Total: <?= number_format($total_conditions) ?> conditions</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($conditions)): ?>
                                <div class="empty-state">
                                    <div class="empty-state-icon">🔧</div>
                                    <h3 class="empty-state-title">No Conditions Found</h3>
                                    <p class="empty-state-description">
                                        <?php if (!empty($search)): ?>
                                            No conditions match your search criteria. Try adjusting your search terms.
                                        <?php else: ?>
                                            Start by adding your first condition to categorize book quality.
                                        <?php endif; ?>
                                    </p>
                                    <div class="empty-state-actions">
                                        <?php if (!empty($search)): ?>
                                            <a href="?" class="btn btn-secondary">Clear Search</a>
                                        <?php endif; ?>
                                        <a href="?action=add" class="btn btn-primary">Add First Condition</a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Description</th>
                                                <th>Books</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($conditions as $condition): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?= htmlspecialchars($condition['name']) ?></strong>
                                                    </td>
                                                    <td>
                                                        <?php if (!empty($condition['description'])): ?>
                                                            <?= htmlspecialchars($condition['description']) ?>
                                                        <?php else: ?>
                                                            <span class="text-muted">—</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-info"><?= number_format($condition['bookCount']) ?> books</span>
                                                    </td>
                                                    <td>
                                                        <span class="text-muted"><?= date('M j, Y', strtotime($condition['createdAt'])) ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <a href="?action=edit&id=<?= urlencode($condition['id']) ?>"
                                                               class="btn btn-sm btn-outline" title="Edit Condition">
                                                                ✏️
                                                            </a>
                                                            <?php if ($condition['bookCount'] == 0): ?>
                                                                <button type="button"
                                                                        class="btn btn-sm btn-danger delete-condition"
                                                                        data-id="<?= htmlspecialchars($condition['id']) ?>"
                                                                        data-name="<?= htmlspecialchars($condition['name']) ?>"
                                                                        title="Delete Condition">
                                                                    🗑️
                                                                </button>
                                                            <?php else: ?>
                                                                <button type="button"
                                                                        class="btn btn-sm btn-outline"
                                                                        disabled
                                                                        title="Cannot delete - has <?= $condition['bookCount'] ?> books">
                                                                    🔒
                                                                </button>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <?php if ($total_conditions > $per_page): ?>
                                    <div class="pagination-wrapper">
                                        <?php
                                        $total_pages = ceil($total_conditions / $per_page);
                                        $query_params = $_GET;
                                        ?>
                                        <div class="pagination">
                                            <?php if ($page > 1): ?>
                                                <?php $query_params['page'] = $page - 1; ?>
                                                <a href="?<?= http_build_query($query_params) ?>" class="pagination-btn">‹ Previous</a>
                                            <?php endif; ?>

                                            <?php
                                            $start_page = max(1, $page - 2);
                                            $end_page = min($total_pages, $page + 2);

                                            for ($i = $start_page; $i <= $end_page; $i++):
                                                $query_params['page'] = $i;
                                            ?>
                                                <a href="?<?= http_build_query($query_params) ?>"
                                                   class="pagination-btn <?= $i === $page ? 'active' : '' ?>">
                                                    <?= $i ?>
                                                </a>
                                            <?php endfor; ?>

                                            <?php if ($page < $total_pages): ?>
                                                <?php $query_params['page'] = $page + 1; ?>
                                                <a href="?<?= http_build_query($query_params) ?>" class="pagination-btn">Next ›</a>
                                            <?php endif; ?>
                                        </div>
                                        <div class="pagination-info">
                                            Showing <?= number_format(($page - 1) * $per_page + 1) ?> to
                                            <?= number_format(min($page * $per_page, $total_conditions)) ?> of
                                            <?= number_format($total_conditions) ?> conditions
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                <?php elseif ($action === 'add' || $action === 'edit'): ?>
                    <!-- Add/Edit Condition Form -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <h2 class="page-title"><?= $action === 'add' ? 'Add New Condition' : 'Edit Condition' ?></h2>
                            <p class="page-subtitle">
                                <?= $action === 'add' ? 'Enter condition information to add to the library system' : 'Update condition information' ?>
                            </p>
                        </div>
                        <div class="page-header-actions">
                            <a href="?" class="btn btn-secondary">
                                <span style="margin-right: 0.5rem;">←</span>
                                Back to Conditions
                            </a>
                        </div>
                    </div>

                    <form method="POST" class="condition-form">
                        <input type="hidden" name="action" value="<?= $action ?>">
                        <?php if ($action === 'edit' && $condition_data): ?>
                            <input type="hidden" name="id" value="<?= htmlspecialchars($condition_data['id']) ?>">
                        <?php endif; ?>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Condition Information</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-group">
                                    <label for="name" class="label required">Condition Name</label>
                                    <input type="text"
                                           id="name"
                                           name="name"
                                           class="input"
                                           placeholder="Enter condition name (e.g., Excellent, Good, Fair, Poor)"
                                           value="<?= htmlspecialchars($condition_data['name'] ?? '') ?>"
                                           required>
                                    <div class="help-text">The name of the condition (required)</div>
                                </div>

                                <div class="form-group">
                                    <label for="description" class="label">Description</label>
                                    <textarea id="description"
                                              name="description"
                                              class="textarea"
                                              rows="3"
                                              placeholder="Enter condition description or criteria..."><?= htmlspecialchars($condition_data['description'] ?? '') ?></textarea>
                                    <div class="help-text">Description or criteria for this condition (optional)</div>
                                </div>

                                <div class="form-group">
                                    <label for="sortOrder" class="label">Sort Order</label>
                                    <input type="number"
                                           id="sortOrder"
                                           name="sortOrder"
                                           class="input"
                                           placeholder="Enter sort order (lower numbers appear first)"
                                           value="<?= htmlspecialchars($condition_data['sortOrder'] ?? '') ?>"
                                           min="0">
                                    <div class="help-text">Sort order for displaying conditions (optional, lower numbers appear first)</div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <span style="margin-right: 0.5rem;">💾</span>
                                <?= $action === 'add' ? 'Add Condition' : 'Update Condition' ?>
                            </button>
                            <a href="?" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Confirm Delete</h3>
                <button type="button" class="modal-close" onclick="closeDeleteModal()">×</button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the condition "<span id="deleteConditionName"></span>"?</p>
                <p class="text-warning">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeDeleteModal()">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">Delete Condition</button>
            </div>
        </div>
    </div>

    <script>
        // Mobile Menu Functionality
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');

            sidebar.classList.toggle('open');
            overlay.classList.toggle('active');

            if (sidebar.classList.contains('open')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        function closeMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');

            sidebar.classList.remove('open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        // Delete functionality
        let deleteConditionId = null;

        function openDeleteModal(id, name) {
            deleteConditionId = id;
            document.getElementById('deleteConditionName').textContent = name;
            document.getElementById('deleteModal').style.display = 'flex';
        }

        function closeDeleteModal() {
            deleteConditionId = null;
            document.getElementById('deleteModal').style.display = 'none';
        }

        function confirmDelete() {
            if (deleteConditionId) {
                // Send AJAX request to delete
                fetch(`?ajax_delete=1&id=${encodeURIComponent(deleteConditionId)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Reload page to show updated list
                            window.location.reload();
                        } else {
                            alert('Error: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while deleting the condition.');
                    });
            }
            closeDeleteModal();
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Mobile menu toggle button event listener
            const mobileToggle = document.getElementById('mobile-menu-toggle');
            if (mobileToggle) {
                mobileToggle.addEventListener('click', toggleMobileMenu);
            }

            // Mobile overlay click event listener
            const mobileOverlay = document.getElementById('mobile-overlay');
            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', closeMobileMenu);
            }

            // Mobile close button event listener
            const mobileCloseBtn = document.getElementById('mobile-close-btn');
            if (mobileCloseBtn) {
                mobileCloseBtn.addEventListener('click', closeMobileMenu);
            }

            // Delete button event listeners
            document.querySelectorAll('.delete-condition').forEach(button => {
                button.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const name = this.getAttribute('data-name');
                    openDeleteModal(id, name);
                });
            });

            // Close mobile menu when clicking on nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth <= 1023) {
                        closeMobileMenu();
                    }
                });
            });

            // Handle escape key to close modal
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    closeDeleteModal();
                    closeMobileMenu();
                }
            });

            console.log('Conditions page loaded successfully');
        });

        // Close mobile menu on window resize if screen becomes large
        window.addEventListener('resize', () => {
            if (window.innerWidth > 1023) {
                closeMobileMenu();
            }
        });
    </script>
</body>
</html>
