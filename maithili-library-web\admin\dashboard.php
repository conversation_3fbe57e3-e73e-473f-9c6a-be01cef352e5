<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Admin Dashboard
 */

require_once '../config/config.php';
require_once '../includes/Database.php';
require_once '../includes/functions.php';

// Check if user is logged in
require_admin();

// Initialize database
$db = new Database();

// Get dashboard statistics using direct SQL queries
try {
    // Get real statistics using direct SQL queries (like reports page)
    $total_books = $db->fetchColumn("SELECT COUNT(*) FROM books WHERE isDeleted = 0") ?: 0;
    $total_authors = $db->fetchColumn("SELECT COUNT(*) FROM authors WHERE isDeleted = 0") ?: 0;
    $total_categories = $db->fetchColumn("SELECT COUNT(*) FROM categories WHERE isDeleted = 0") ?: 0;
    $total_publishers = $db->fetchColumn("SELECT COUNT(*) FROM publishers WHERE isDeleted = 0") ?: 0;
    $total_languages = $db->fetchColumn("SELECT COUNT(*) FROM languages WHERE isDeleted = 0") ?: 0;

    // Create stats array in the same format for compatibility
    $stats = [
        'books' => ['count' => $total_books],
        'authors' => ['count' => $total_authors],
        'categories' => ['count' => $total_categories],
        'publishers' => ['count' => $total_publishers],
        'languages' => ['count' => $total_languages]
    ];

    // Get additional dashboard data
    $recent_books_count = 0;
    $recent_authors_count = 0;
    $total_admin_users = 1;

    // Check if tables exist before querying
    if ($db->tableExists('books')) {
        $recent_books_count = $db->fetchColumn("
            SELECT COUNT(*) FROM books
            WHERE isDeleted = 0 AND createdAt >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ") ?: 0;
    }

    if ($db->tableExists('authors')) {
        $recent_authors_count = $db->fetchColumn("
            SELECT COUNT(*) FROM authors
            WHERE isDeleted = 0 AND createdAt >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ") ?: 0;
    }

    if ($db->tableExists('users')) {
        // Check if users table has isDeleted column
        $columns = $db->getTableStructure('users');
        $has_isDeleted = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'isDeleted') {
                $has_isDeleted = true;
                break;
            }
        }

        if ($has_isDeleted) {
            $total_admin_users = $db->fetchColumn("SELECT COUNT(*) FROM users WHERE isDeleted = 0") ?: 1;
        } else {
            $total_admin_users = $db->fetchColumn("SELECT COUNT(*) FROM users") ?: 1;
        }
    }

    // Get top categories and languages for collection summary
    try {
        $stats['top_categories'] = $db->fetchAll("
            SELECT c.name, c.nameNepali, COUNT(b.id) as book_count
            FROM categories c
            LEFT JOIN books b ON c.id = b.categoryId AND b.isDeleted = 0
            WHERE c.isDeleted = 0
            GROUP BY c.id, c.name, c.nameNepali
            HAVING book_count > 0
            ORDER BY book_count DESC
            LIMIT 5
        ");

        $stats['top_languages'] = $db->fetchAll("
            SELECT l.name, l.nameNepali, COUNT(b.id) as book_count
            FROM languages l
            LEFT JOIN books b ON l.id = b.languageId AND b.isDeleted = 0
            WHERE l.isDeleted = 0
            GROUP BY l.id, l.name, l.nameNepali
            HAVING book_count > 0
            ORDER BY book_count DESC
            LIMIT 5
        ");
    } catch (Exception $e) {
        $stats['top_categories'] = [];
        $stats['top_languages'] = [];
    }

} catch (Exception $e) {
    log_activity("Dashboard stats error: " . $e->getMessage(), 'ERROR');
    $stats = [
        'books' => ['count' => 0],
        'authors' => ['count' => 0],
        'categories' => ['count' => 0],
        'publishers' => ['count' => 0],
        'languages' => ['count' => 0],
        'top_categories' => [],
        'top_languages' => []
    ];
    $recent_books_count = 0;
    $recent_authors_count = 0;
    $total_admin_users = 1;
}

// Get recent activities (if user_logs table exists)
$recent_activities = [];
try {
    $recent_activities = $db->fetchAll(
        "SELECT ul.*, u.name as user_name 
         FROM user_logs ul 
         LEFT JOIN users u ON ul.userId = u.id 
         ORDER BY ul.createdAt DESC 
         LIMIT 10"
    );
} catch (Exception $e) {
    // Table might not exist yet
}

$page_title = 'Dashboard - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="<?= asset_url('css/style.css') ?>">
    <link rel="stylesheet" href="<?= asset_url('css/admin.css') ?>">
    
    <!-- Favicon and App Icons -->
    <?= generate_favicon_tags() ?>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <button class="mobile-close-btn" id="mobile-close-btn">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <div class="sidebar-logo">
                    <img src="<?= icon_url(48) ?>" alt="<?= APP_NAME ?> Icon" class="sidebar-logo-image">
                </div>
                <h2 class="sidebar-title">Admin Panel</h2>
                <p class="sidebar-subtitle">मैथिली विकास कोष</p>
            </div>

            <!-- Mobile User Section -->
            <div class="mobile-user-section">
                <div class="mobile-user-info">
                    <div class="mobile-user-avatar">
                        <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                    </div>
                    <div class="mobile-user-details">
                        <span class="mobile-user-name"><?= htmlspecialchars($_SESSION['admin_name']) ?></span>
                        <span class="mobile-user-role">Administrator</span>
                    </div>
                </div>
                <a href="logout.php" class="mobile-logout-btn">
                    <span class="logout-icon">🚪</span>
                    Logout
                </a>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <div class="nav-item">
                        <a href="dashboard.php" class="nav-link active">
                            <span class="nav-icon">📊</span>
                            Dashboard
                        </a>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Book Management</div>
                    <div class="nav-item">
                        <a href="books.php" class="nav-link">
                            <span class="nav-icon">📚</span>
                            Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="books.php?action=add" class="nav-link">
                            <span class="nav-icon">➕</span>
                            Add Book
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-books.php" class="nav-link">
                            <span class="nav-icon">📥</span>
                            Import Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-images.php" class="nav-link">
                            <span class="nav-icon">🖼️</span>
                            Import Images
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Catalog Management</div>
                    <div class="nav-item">
                        <a href="authors.php" class="nav-link">
                            <span class="nav-icon">✍️</span>
                            Authors
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="categories.php" class="nav-link">
                            <span class="nav-icon">🏷️</span>
                            Categories
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="publishers.php" class="nav-link">
                            <span class="nav-icon">🏢</span>
                            Publishers
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="languages.php" class="nav-link">
                            <span class="nav-icon">🌐</span>
                            Languages
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="subjects.php" class="nav-link">
                            <span class="nav-icon">📖</span>
                            Subjects
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="series.php" class="nav-link">
                            <span class="nav-icon">📑</span>
                            Book Series
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="locations.php" class="nav-link">
                            <span class="nav-icon">📍</span>
                            Locations
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="conditions.php" class="nav-link">
                            <span class="nav-icon">🔧</span>
                            Conditions
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="sources.php" class="nav-link">
                            <span class="nav-icon">🎁</span>
                            Sources
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Reports & Analytics</div>
                    <div class="nav-item">
                        <a href="reports.php" class="nav-link">
                            <span class="nav-icon">📈</span>
                            Collection Reports
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="analytics.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Analytics
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="contact_inquiries.php" class="nav-link">
                            <span class="nav-icon">💬</span>
                            Contact Inquiries
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <div class="nav-item">
                        <a href="users.php" class="nav-link">
                            <span class="nav-icon">👤</span>
                            Admin Users
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="settings.php" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            Settings
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="backup.php" class="nav-link">
                            <span class="nav-icon">💾</span>
                            Backup & Export
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="../database_reset.php" class="nav-link" style="color: #ef4444;">
                            <span class="nav-icon">🗑️</span>
                            Database Reset
                        </a>
                    </div>
                </div>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <header class="admin-header">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="header-title">
                            <span class="title-icon">📊</span>
                            Dashboard
                        </h1>
                        <p class="header-subtitle">Library management overview</p>
                    </div>
                    <div class="header-actions">
                        <div class="user-info">
                            <span class="welcome-text">Welcome, <?= htmlspecialchars($_SESSION['admin_name']) ?>!</span>
                            <div class="user-menu">
                                <div class="user-avatar">
                                    <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                                </div>
                            </div>
                        </div>
                        <a href="logout.php" class="btn btn-secondary btn-sm">
                            <span style="margin-right: 0.5rem;">🚪</span>
                            Logout
                        </a>
                    </div>
                </div>
            </header>
            
            <!-- Content -->
            <div class="admin-content">
                <!-- Breadcrumb -->
                <nav class="breadcrumb">
                    <span class="breadcrumb-item active">Dashboard</span>
                </nav>
                
                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card primary">
                        <div class="stat-icon">📚</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($stats['books']['count'] ?? 0) ?></div>
                            <div class="stat-label">Total Books</div>
                            <?php if ($recent_books_count > 0): ?>
                                <div class="stat-change positive">
                                    +<?= $recent_books_count ?> this month
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="stat-card accent">
                        <div class="stat-icon">✍️</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($stats['authors']['count'] ?? 0) ?></div>
                            <div class="stat-label">Authors</div>
                            <?php if ($recent_authors_count > 0): ?>
                                <div class="stat-change positive">
                                    +<?= $recent_authors_count ?> this month
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="stat-card info">
                        <div class="stat-icon">🏷️</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($stats['categories']['count'] ?? 0) ?></div>
                            <div class="stat-label">Categories</div>
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-icon">🏢</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($stats['publishers']['count'] ?? 0) ?></div>
                            <div class="stat-label">Publishers</div>
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-icon">🌐</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($stats['languages']['count'] ?? 0) ?></div>
                            <div class="stat-label">Languages</div>
                        </div>
                    </div>

                    <div class="stat-card secondary">
                        <div class="stat-icon">👤</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($total_admin_users) ?></div>
                            <div class="stat-label">Admin Users</div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span style="margin-right: 0.5rem;">⚡</span>
                            Quick Actions
                        </h3>
                    </div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 1.5rem;">
                            <a href="books.php?action=add" class="quick-action-card primary">
                                <div class="action-icon">📚</div>
                                <div class="action-content">
                                    <h4>Add New Book</h4>
                                    <p>Add a single book to the collection</p>
                                </div>
                            </a>
                            <a href="import-books.php" class="quick-action-card secondary">
                                <div class="action-icon">📥</div>
                                <div class="action-content">
                                    <h4>Import Books</h4>
                                    <p>Bulk import from CSV or Excel</p>
                                </div>
                            </a>
                            <a href="authors.php?action=add" class="quick-action-card accent">
                                <div class="action-icon">✍️</div>
                                <div class="action-content">
                                    <h4>Add Author</h4>
                                    <p>Create new author profile</p>
                                </div>
                            </a>
                            <a href="categories.php?action=add" class="quick-action-card accent">
                                <div class="action-icon">🏷️</div>
                                <div class="action-content">
                                    <h4>Add Category</h4>
                                    <p>Create book category</p>
                                </div>
                            </a>
                            <a href="reports.php" class="quick-action-card info">
                                <div class="action-icon">📈</div>
                                <div class="action-content">
                                    <h4>View Reports</h4>
                                    <p>Collection analytics & reports</p>
                                </div>
                            </a>
                            <a href="backup.php" class="quick-action-card warning">
                                <div class="action-icon">💾</div>
                                <div class="action-content">
                                    <h4>Backup Data</h4>
                                    <p>Export & backup collection</p>
                                </div>
                            </a>
                            <a href="../database_reset.php" class="quick-action-card" style="border-color: #ef4444; color: #ef4444;">
                                <div class="action-icon" style="color: #ef4444;">🗑️</div>
                                <div class="action-content">
                                    <h4 style="color: #ef4444;">Database Reset</h4>
                                    <p style="color: #666;">Clear all data (preserve users)</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Collection Summary -->
                <div class="card" style="margin-top: 1.5rem;">
                    <div class="card-header">
                        <h3 class="card-title">
                            <span style="margin-right: 0.5rem;">📊</span>
                            Collection Summary
                        </h3>
                    </div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem;">
                            <?php if (!empty($stats['top_categories'])): ?>
                                <div>
                                    <h4 style="color: var(--primary-color); margin-bottom: 1rem; font-size: 1rem;">Top Categories</h4>
                                    <?php foreach (array_slice($stats['top_categories'], 0, 3) as $category): ?>
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="font-size: 0.875rem;"><?= htmlspecialchars($category['name']) ?></span>
                                            <span style="font-weight: 600; color: var(--primary-color);"><?= $category['book_count'] ?></span>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($stats['top_languages'])): ?>
                                <div>
                                    <h4 style="color: var(--primary-color); margin-bottom: 1rem; font-size: 1rem;">Top Languages</h4>
                                    <?php foreach (array_slice($stats['top_languages'], 0, 3) as $language): ?>
                                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                            <span style="font-size: 0.875rem;"><?= htmlspecialchars($language['name']) ?></span>
                                            <span style="font-weight: 600; color: var(--primary-color);"><?= $language['book_count'] ?></span>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>

                            <div>
                                <h4 style="color: var(--primary-color); margin-bottom: 1rem; font-size: 1rem;">System Status</h4>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span style="font-size: 0.875rem;">Database</span>
                                    <span style="font-weight: 600; color: var(--success-color);">Connected</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span style="font-size: 0.875rem;">Mode</span>
                                    <span style="font-weight: 600; color: <?= DEVELOPMENT_MODE ? 'var(--warning-color)' : 'var(--success-color)' ?>;">
                                        <?= DEVELOPMENT_MODE ? 'Development' : 'Production' ?>
                                    </span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                                    <span style="font-size: 0.875rem;">Version</span>
                                    <span style="font-weight: 600; color: var(--text-dark);"><?= APP_VERSION ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <script>
        // Mobile Menu Functionality - Define functions globally first
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');

            sidebar.classList.toggle('open');
            overlay.classList.toggle('active');

            // Prevent body scroll when menu is open
            if (sidebar.classList.contains('open')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        function closeMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');

            sidebar.classList.remove('open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Mobile menu toggle button event listener
            const mobileToggle = document.getElementById('mobile-menu-toggle');
            if (mobileToggle) {
                mobileToggle.addEventListener('click', toggleMobileMenu);
            }

            // Mobile overlay click event listener
            const mobileOverlay = document.getElementById('mobile-overlay');
            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', closeMobileMenu);
            }

            // Mobile close button event listener
            const mobileCloseBtn = document.getElementById('mobile-close-btn');
            if (mobileCloseBtn) {
                mobileCloseBtn.addEventListener('click', closeMobileMenu);
            }

            // Close mobile menu when clicking on nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth <= 1023) {
                        closeMobileMenu();
                    }
                });
            });

            // Add active class to current page nav link
            const currentPage = window.location.pathname.split('/').pop();
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href && (href === currentPage || href.includes(currentPage))) {
                    link.classList.add('active');
                }
            });

            // Enhanced card hover effects (desktop only)
            if (window.innerWidth > 1023) {
                document.querySelectorAll('.quick-action-card').forEach(card => {
                    card.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateY(-5px) scale(1.02)';
                    });

                    card.addEventListener('mouseleave', function() {
                        this.style.transform = '';
                    });
                });
            }

            console.log('Admin dashboard loaded with responsive features');
        });

        // Close mobile menu on window resize if screen becomes large
        window.addEventListener('resize', () => {
            if (window.innerWidth > 1023) {
                closeMobileMenu();
            }
        });

        // Handle escape key to close mobile menu
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeMobileMenu();
            }
        });

        // Auto-hide mobile menu on scroll
        let lastScrollTop = 0;
        window.addEventListener('scroll', () => {
            const currentScroll = window.pageYOffset || document.documentElement.scrollTop;

            if (currentScroll > lastScrollTop && currentScroll > 100) {
                // Scrolling down - hide mobile menu
                if (window.innerWidth <= 1023) {
                    closeMobileMenu();
                }
            }

            lastScrollTop = currentScroll <= 0 ? 0 : currentScroll;
        });
    </script>
</body>
</html>
