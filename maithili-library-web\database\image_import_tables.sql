-- Image Import Tables for मैथिली विकास कोष
-- Tables for managing bulk image import functionality

SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;

-- Image Import Jobs table
CREATE TABLE IF NOT EXISTS `image_import_jobs` (
  `id` varchar(50) NOT NULL,
  `userId` varchar(50) NOT NULL,
  `matchingMethod` varchar(50) NOT NULL DEFAULT 'filename',
  `overwriteExisting` tinyint(1) NOT NULL DEFAULT 0,
  `status` varchar(50) NOT NULL DEFAULT 'PENDING',
  `totalFiles` int(11) DEFAULT 0,
  `processedFiles` int(11) DEFAULT 0,
  `matchedFiles` int(11) DEFAULT 0,
  `errorFiles` int(11) DEFAULT 0,
  `skippedFiles` int(11) DEFAULT 0,
  `startTime` datetime DEFAULT NULL,
  `endTime` datetime DEFAULT NULL,
  `errorMessage` text DEFAULT NULL,
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_image_import_jobs_userId` (`userId`),
  KEY `idx_image_import_jobs_status` (`status`),
  KEY `idx_image_import_jobs_createdAt` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Image Import Files table
CREATE TABLE IF NOT EXISTS `image_import_files` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `jobId` varchar(50) NOT NULL,
  `originalName` varchar(255) NOT NULL,
  `filePath` varchar(500) NOT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'PENDING',
  `bookId` varchar(50) DEFAULT NULL,
  `accessionNo` varchar(100) DEFAULT NULL,
  `errorMessage` text DEFAULT NULL,
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_image_import_files_jobId` (`jobId`),
  KEY `idx_image_import_files_status` (`status`),
  KEY `idx_image_import_files_bookId` (`bookId`),
  KEY `idx_image_import_files_accessionNo` (`accessionNo`),
  CONSTRAINT `fk_image_import_files_jobId` FOREIGN KEY (`jobId`) REFERENCES `image_import_jobs` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_image_import_files_bookId` FOREIGN KEY (`bookId`) REFERENCES `books` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

SET FOREIGN_KEY_CHECKS = 1;
COMMIT;
