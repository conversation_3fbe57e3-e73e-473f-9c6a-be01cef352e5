<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Collection Reports
 */

require_once '../config/config.php';
require_once '../includes/Database.php';
require_once '../includes/functions.php';

// Check if user is logged in
require_admin();

// Initialize database
$db = new Database();

$message = '';
$error = '';

// Get report data
try {
    // Basic statistics
    $total_books = $db->fetchColumn("SELECT COUNT(*) FROM books WHERE isDeleted = 0");
    $total_authors = $db->fetchColumn("SELECT COUNT(*) FROM authors WHERE isDeleted = 0");
    $total_categories = $db->fetchColumn("SELECT COUNT(*) FROM categories WHERE isDeleted = 0");
    $total_publishers = $db->fetchColumn("SELECT COUNT(*) FROM publishers WHERE isDeleted = 0");
    $total_languages = $db->fetchColumn("SELECT COUNT(*) FROM languages WHERE isDeleted = 0");
    $total_subjects = $db->fetchColumn("SELECT COUNT(*) FROM subjects WHERE isDeleted = 0");
    $total_series = $db->fetchColumn("SELECT COUNT(*) FROM book_series WHERE isDeleted = 0");
    $total_locations = $db->fetchColumn("SELECT COUNT(*) FROM locations WHERE isDeleted = 0");
    $total_conditions = $db->fetchColumn("SELECT COUNT(*) FROM conditions");
    $total_sources = $db->fetchColumn("SELECT COUNT(*) FROM sources WHERE isDeleted = 0");

    // Books by category
    $books_by_category = $db->fetchAll("
        SELECT c.name, c.nameNepali, COUNT(b.id) as bookCount
        FROM categories c
        LEFT JOIN books b ON c.id = b.categoryId AND b.isDeleted = 0
        WHERE c.isDeleted = 0
        GROUP BY c.id
        ORDER BY bookCount DESC, c.name ASC
        LIMIT 10
    ");

    // Books by language
    $books_by_language = $db->fetchAll("
        SELECT l.name, l.nameNepali, COUNT(b.id) as bookCount
        FROM languages l
        LEFT JOIN books b ON l.id = b.languageId AND b.isDeleted = 0
        WHERE l.isDeleted = 0
        GROUP BY l.id
        ORDER BY bookCount DESC, l.name ASC
        LIMIT 10
    ");

    // Books by condition
    $books_by_condition = $db->fetchAll("
        SELECT c.name, COUNT(b.id) as bookCount
        FROM conditions c
        LEFT JOIN books b ON c.id = b.conditionId AND b.isDeleted = 0
        GROUP BY c.id
        ORDER BY c.sortOrder ASC, c.name ASC
    ");

    // Top authors by book count
    $top_authors = $db->fetchAll("
        SELECT a.name, a.nameNepali, COUNT(b.id) as bookCount
        FROM authors a
        LEFT JOIN books b ON a.id = b.authorId AND b.isDeleted = 0
        WHERE a.isDeleted = 0
        GROUP BY a.id
        HAVING bookCount > 0
        ORDER BY bookCount DESC, a.name ASC
        LIMIT 10
    ");

    // Recent additions
    $recent_books = $db->fetchAll("
        SELECT b.title, b.titleNepali, a.name as authorName, c.name as categoryName, b.createdAt
        FROM books b
        LEFT JOIN authors a ON b.authorId = a.id
        LEFT JOIN categories c ON b.categoryId = c.id
        WHERE b.isDeleted = 0
        ORDER BY b.createdAt DESC
        LIMIT 10
    ");

} catch (Exception $e) {
    $error = 'Database error: ' . $e->getMessage();
    // log_activity("Reports error: " . $e->getMessage(), 'ERROR');
}

$page_title = 'Collection Reports - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="<?= asset_url('css/style.css') ?>">
    <link rel="stylesheet" href="<?= asset_url('css/admin.css') ?>">
    
    <!-- Favicon and App Icons -->
    <?= generate_favicon_tags() ?>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <button class="mobile-close-btn" id="mobile-close-btn">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <div class="sidebar-logo">
                    <img src="<?= icon_url(48) ?>" alt="<?= APP_NAME ?> Icon" class="sidebar-logo-image">
                </div>
                <h2 class="sidebar-title">Admin Panel</h2>
                <p class="sidebar-subtitle">मैथिली विकास कोष</p>
            </div>

            <!-- Mobile User Section -->
            <div class="mobile-user-section">
                <div class="mobile-user-info">
                    <div class="mobile-user-avatar">
                        <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                    </div>
                    <div class="mobile-user-details">
                        <span class="mobile-user-name"><?= htmlspecialchars($_SESSION['admin_name']) ?></span>
                        <span class="mobile-user-role">Administrator</span>
                    </div>
                </div>
                <a href="logout.php" class="mobile-logout-btn">
                    <span class="logout-icon">🚪</span>
                    Logout
                </a>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <div class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Dashboard
                        </a>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Book Management</div>
                    <div class="nav-item">
                        <a href="books.php" class="nav-link">
                            <span class="nav-icon">📚</span>
                            Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="books.php?action=add" class="nav-link">
                            <span class="nav-icon">➕</span>
                            Add Book
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-books.php" class="nav-link">
                            <span class="nav-icon">📥</span>
                            Import Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-images.php" class="nav-link">
                            <span class="nav-icon">🖼️</span>
                            Import Images
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Catalog Management</div>
                    <div class="nav-item">
                        <a href="authors.php" class="nav-link">
                            <span class="nav-icon">✍️</span>
                            Authors
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="categories.php" class="nav-link">
                            <span class="nav-icon">🏷️</span>
                            Categories
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="publishers.php" class="nav-link">
                            <span class="nav-icon">🏢</span>
                            Publishers
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="languages.php" class="nav-link">
                            <span class="nav-icon">🌐</span>
                            Languages
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="subjects.php" class="nav-link">
                            <span class="nav-icon">📖</span>
                            Subjects
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="series.php" class="nav-link">
                            <span class="nav-icon">📑</span>
                            Book Series
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="locations.php" class="nav-link">
                            <span class="nav-icon">📍</span>
                            Locations
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="conditions.php" class="nav-link">
                            <span class="nav-icon">🔧</span>
                            Conditions
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="sources.php" class="nav-link">
                            <span class="nav-icon">🎁</span>
                            Sources
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Reports & Analytics</div>
                    <div class="nav-item">
                        <a href="reports.php" class="nav-link active">
                            <span class="nav-icon">📈</span>
                            Collection Reports
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="analytics.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Analytics
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="contact_inquiries.php" class="nav-link">
                            <span class="nav-icon">💬</span>
                            Contact Inquiries
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <div class="nav-item">
                        <a href="users.php" class="nav-link">
                            <span class="nav-icon">👤</span>
                            Admin Users
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="settings.php" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            Settings
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="backup.php" class="nav-link">
                            <span class="nav-icon">💾</span>
                            Backup & Export
                        </a>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>

            <!-- Header -->
            <header class="admin-header">
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="header-title">
                            <span class="title-icon">📈</span>
                            Reports
                        </h1>
                        <p class="header-subtitle">Collection statistics and insights</p>
                    </div>
                    <div class="header-actions">
                        <div class="user-info">
                            <span class="welcome-text">Welcome, <?= htmlspecialchars($_SESSION['admin_name']) ?>!</span>
                            <div class="user-menu">
                                <div class="user-avatar">
                                    <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                                </div>
                            </div>
                        </div>
                        <a href="logout.php" class="btn btn-secondary btn-sm">
                            <span style="margin-right: 0.5rem;">🚪</span>
                            Logout
                        </a>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Breadcrumb -->
                <nav class="breadcrumb">
                    <a href="dashboard.php" class="breadcrumb-item">Dashboard</a>
                    <span class="breadcrumb-separator">›</span>
                    <span class="breadcrumb-item active">Collection Reports</span>
                </nav>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <span class="alert-icon">✅</span>
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <span class="alert-icon">❌</span>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <!-- Statistics Cards -->
                <div class="stats-grid">
                    <div class="stat-card primary">
                        <div class="stat-icon">📚</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($total_books) ?></div>
                            <div class="stat-label">Total Books</div>
                        </div>
                    </div>
                    <div class="stat-card accent">
                        <div class="stat-icon">✍️</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($total_authors) ?></div>
                            <div class="stat-label">Authors</div>
                        </div>
                    </div>
                    <div class="stat-card info">
                        <div class="stat-icon">🏷️</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($total_categories) ?></div>
                            <div class="stat-label">Categories</div>
                        </div>
                    </div>
                    <div class="stat-card success">
                        <div class="stat-icon">🏢</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($total_publishers) ?></div>
                            <div class="stat-label">Publishers</div>
                        </div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-icon">🌐</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($total_languages) ?></div>
                            <div class="stat-label">Languages</div>
                        </div>
                    </div>
                    <div class="stat-card secondary">
                        <div class="stat-icon">📖</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($total_subjects) ?></div>
                            <div class="stat-label">Subjects</div>
                        </div>
                    </div>
                    <div class="stat-card primary">
                        <div class="stat-icon">📑</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($total_series) ?></div>
                            <div class="stat-label">Book Series</div>
                        </div>
                    </div>
                    <div class="stat-card accent">
                        <div class="stat-icon">📍</div>
                        <div class="stat-content">
                            <div class="stat-number"><?= number_format($total_locations) ?></div>
                            <div class="stat-label">Locations</div>
                        </div>
                    </div>
                </div>

                <!-- Reports Grid -->
                <div class="reports-grid">
                    <!-- Books by Category -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Books by Category</h3>
                            <div class="card-header-actions">
                                <span class="text-muted">Top 10 categories</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($books_by_category)): ?>
                                <div class="empty-state-small">
                                    <div class="empty-state-icon">🏷️</div>
                                    <p>No categories found</p>
                                </div>
                            <?php else: ?>
                                <div class="report-list">
                                    <?php foreach ($books_by_category as $category): ?>
                                        <div class="report-item">
                                            <div class="report-item-content">
                                                <div class="report-item-title"><?= htmlspecialchars($category['name']) ?></div>
                                                <?php if (!empty($category['nameNepali'])): ?>
                                                    <div class="report-item-subtitle nepali-text"><?= htmlspecialchars($category['nameNepali']) ?></div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="report-item-value">
                                                <span class="badge badge-info"><?= number_format($category['bookCount']) ?></span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Books by Language -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Books by Language</h3>
                            <div class="card-header-actions">
                                <span class="text-muted">Top 10 languages</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($books_by_language)): ?>
                                <div class="empty-state-small">
                                    <div class="empty-state-icon">🌐</div>
                                    <p>No languages found</p>
                                </div>
                            <?php else: ?>
                                <div class="report-list">
                                    <?php foreach ($books_by_language as $language): ?>
                                        <div class="report-item">
                                            <div class="report-item-content">
                                                <div class="report-item-title"><?= htmlspecialchars($language['name']) ?></div>
                                                <?php if (!empty($language['nameNepali'])): ?>
                                                    <div class="report-item-subtitle nepali-text"><?= htmlspecialchars($language['nameNepali']) ?></div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="report-item-value">
                                                <span class="badge badge-success"><?= number_format($language['bookCount']) ?></span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Books by Condition -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Books by Condition</h3>
                            <div class="card-header-actions">
                                <span class="text-muted">All conditions</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($books_by_condition)): ?>
                                <div class="empty-state-small">
                                    <div class="empty-state-icon">🔧</div>
                                    <p>No conditions found</p>
                                </div>
                            <?php else: ?>
                                <div class="report-list">
                                    <?php foreach ($books_by_condition as $condition): ?>
                                        <div class="report-item">
                                            <div class="report-item-content">
                                                <div class="report-item-title"><?= htmlspecialchars($condition['name']) ?></div>
                                            </div>
                                            <div class="report-item-value">
                                                <span class="badge badge-warning"><?= number_format($condition['bookCount']) ?></span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Top Authors -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Top Authors</h3>
                            <div class="card-header-actions">
                                <span class="text-muted">By book count</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($top_authors)): ?>
                                <div class="empty-state-small">
                                    <div class="empty-state-icon">✍️</div>
                                    <p>No authors found</p>
                                </div>
                            <?php else: ?>
                                <div class="report-list">
                                    <?php foreach ($top_authors as $author): ?>
                                        <div class="report-item">
                                            <div class="report-item-content">
                                                <div class="report-item-title"><?= htmlspecialchars($author['name']) ?></div>
                                                <?php if (!empty($author['nameNepali'])): ?>
                                                    <div class="report-item-subtitle nepali-text"><?= htmlspecialchars($author['nameNepali']) ?></div>
                                                <?php endif; ?>
                                            </div>
                                            <div class="report-item-value">
                                                <span class="badge badge-primary"><?= number_format($author['bookCount']) ?></span>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Recent Additions -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Recent Additions</h3>
                        <div class="card-header-actions">
                            <span class="text-muted">Last 10 books added</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (empty($recent_books)): ?>
                            <div class="empty-state">
                                <div class="empty-state-icon">📚</div>
                                <h3 class="empty-state-title">No Recent Books</h3>
                                <p class="empty-state-description">No books have been added recently.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Title</th>
                                            <th>Author</th>
                                            <th>Category</th>
                                            <th>Added</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_books as $book): ?>
                                            <tr>
                                                <td>
                                                    <div class="book-title">
                                                        <strong><?= htmlspecialchars($book['title']) ?></strong>
                                                        <?php if (!empty($book['titleNepali'])): ?>
                                                            <div class="book-title-nepali nepali-text"><?= htmlspecialchars($book['titleNepali']) ?></div>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <?php if (!empty($book['authorName'])): ?>
                                                        <?= htmlspecialchars($book['authorName']) ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">—</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (!empty($book['categoryName'])): ?>
                                                        <?= htmlspecialchars($book['categoryName']) ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">—</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="text-muted"><?= date('M j, Y', strtotime($book['createdAt'])) ?></span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <script>
        // Mobile Menu Functionality
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');

            sidebar.classList.toggle('open');
            overlay.classList.toggle('active');

            if (sidebar.classList.contains('open')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        function closeMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');

            sidebar.classList.remove('open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Mobile menu toggle button event listener
            const mobileToggle = document.getElementById('mobile-menu-toggle');
            if (mobileToggle) {
                mobileToggle.addEventListener('click', toggleMobileMenu);
            }

            // Mobile overlay click event listener
            const mobileOverlay = document.getElementById('mobile-overlay');
            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', closeMobileMenu);
            }

            // Mobile close button event listener
            const mobileCloseBtn = document.getElementById('mobile-close-btn');
            if (mobileCloseBtn) {
                mobileCloseBtn.addEventListener('click', closeMobileMenu);
            }

            // Close mobile menu when clicking on nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth <= 1023) {
                        closeMobileMenu();
                    }
                });
            });

            // Handle escape key to close modal
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    closeMobileMenu();
                }
            });

            console.log('Reports page loaded successfully');
        });

        // Close mobile menu on window resize if screen becomes large
        window.addEventListener('resize', () => {
            if (window.innerWidth > 1023) {
                closeMobileMenu();
            }
        });
    </script>
</body>
</html>
