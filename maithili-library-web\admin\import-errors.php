<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Import Errors Viewer
 */

require_once '../config/config.php';
require_once '../includes/Database.php';
require_once '../includes/functions.php';

// Check if user is logged in
require_admin();

// Initialize database
$db = new Database();

// Get job ID
$jobId = $_GET['job_id'] ?? null;
if (!$jobId) {
    header('Location: import-books.php');
    exit;
}

// Get import job
$importJob = $db->fetch("SELECT * FROM import_jobs WHERE id = ?", [$jobId]);
if (!$importJob) {
    header('Location: import-books.php?error=job_not_found');
    exit;
}

// Get import errors
$errors = $db->fetchAll("
    SELECT * FROM import_errors 
    WHERE importJobId = ? 
    ORDER BY rowNumber ASC
", [$jobId]);

// Get skipped records
$skippedRecords = $db->fetchAll("
    SELECT * FROM import_skipped_records 
    WHERE importJobId = ? 
    ORDER BY rowNumber ASC
", [$jobId]);

$page_title = 'Import Errors - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>
    <link rel="stylesheet" href="<?= asset_url('css/style.css') ?>">
    <link rel="stylesheet" href="<?= asset_url('css/admin.css') ?>">
    <?= generate_favicon_tags() ?>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <button class="mobile-close-btn" id="mobile-close-btn">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <div class="sidebar-logo">
                    <img src="<?= icon_url(48) ?>" alt="<?= APP_NAME ?> Icon" class="sidebar-logo-image">
                </div>
                <h2 class="sidebar-title">Admin Panel</h2>
                <p class="sidebar-subtitle">मैथिली विकास कोष</p>
            </div>

            <!-- Mobile User Section -->
            <div class="mobile-user-section">
                <div class="mobile-user-info">
                    <div class="mobile-user-avatar">
                        <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                    </div>
                    <div class="mobile-user-details">
                        <span class="mobile-user-name"><?= htmlspecialchars($_SESSION['admin_name']) ?></span>
                        <span class="mobile-user-role">Administrator</span>
                    </div>
                </div>
                <a href="logout.php" class="mobile-logout-btn">
                    <span class="logout-icon">🚪</span>
                    Logout
                </a>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <div class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Dashboard
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Book Management</div>
                    <div class="nav-item">
                        <a href="books.php" class="nav-link">
                            <span class="nav-icon">📚</span>
                            Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="books.php?action=add" class="nav-link">
                            <span class="nav-icon">➕</span>
                            Add Book
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-books.php" class="nav-link active">
                            <span class="nav-icon">📥</span>
                            Import Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-images.php" class="nav-link">
                            <span class="nav-icon">🖼️</span>
                            Import Images
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Catalog Management</div>
                    <div class="nav-item">
                        <a href="authors.php" class="nav-link">
                            <span class="nav-icon">✍️</span>
                            Authors
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="categories.php" class="nav-link">
                            <span class="nav-icon">🏷️</span>
                            Categories
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="publishers.php" class="nav-link">
                            <span class="nav-icon">🏢</span>
                            Publishers
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="languages.php" class="nav-link">
                            <span class="nav-icon">🌐</span>
                            Languages
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="subjects.php" class="nav-link">
                            <span class="nav-icon">📖</span>
                            Subjects
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="series.php" class="nav-link">
                            <span class="nav-icon">📑</span>
                            Book Series
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="locations.php" class="nav-link">
                            <span class="nav-icon">📍</span>
                            Locations
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="conditions.php" class="nav-link">
                            <span class="nav-icon">🔧</span>
                            Conditions
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="sources.php" class="nav-link">
                            <span class="nav-icon">🎁</span>
                            Sources
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Reports & Analytics</div>
                    <div class="nav-item">
                        <a href="reports.php" class="nav-link">
                            <span class="nav-icon">📈</span>
                            Collection Reports
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="analytics.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Analytics
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <div class="nav-item">
                        <a href="users.php" class="nav-link">
                            <span class="nav-icon">👤</span>
                            Admin Users
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="settings.php" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            Settings
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="backup.php" class="nav-link">
                            <span class="nav-icon">💾</span>
                            Backup & Export
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="../database_reset.php" class="nav-link" style="color: #ef4444;">
                            <span class="nav-icon">🗑️</span>
                            Database Reset
                        </a>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <header class="admin-header">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="header-title">
                            <span class="title-icon">❌</span>
                            Import Errors
                        </h1>
                        <p class="header-subtitle">Job: <?= htmlspecialchars($importJob['fileName']) ?></p>
                    </div>
                    <div class="header-actions">
                        <a href="import-books.php?action=status&job_id=<?= urlencode($jobId) ?>" class="btn btn-secondary btn-sm">
                            ← Back to Import Status
                        </a>
                        <div class="user-info">
                            <span class="welcome-text">Welcome, <?= htmlspecialchars($_SESSION['admin_name']) ?>!</span>
                            <div class="user-menu">
                                <div class="user-avatar">
                                    <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                                </div>
                            </div>
                        </div>
                        <a href="logout.php" class="btn btn-secondary btn-sm">
                            <span style="margin-right: 0.5rem;">🚪</span>
                            Logout
                        </a>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Breadcrumb -->
                <nav class="breadcrumb">
                    <a href="dashboard.php" class="breadcrumb-item">Dashboard</a>
                    <span class="breadcrumb-separator">›</span>
                    <a href="import-books.php" class="breadcrumb-item">Import Books</a>
                    <span class="breadcrumb-separator">›</span>
                    <span class="breadcrumb-item active">Import Errors</span>
                </nav>

                <!-- Import Errors -->
                <?php if (!empty($errors)): ?>
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span style="margin-right: 0.5rem;">❌</span>
                                Processing Errors (<?= count($errors) ?>)
                            </h2>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Row</th>
                                            <th>Error Type</th>
                                            <th>Error Message</th>
                                            <th>Field</th>
                                            <th>Row Data</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($errors as $error): ?>
                                            <tr>
                                                <td><?= $error['rowNumber'] ?></td>
                                                <td>
                                                    <span class="badge badge-error">
                                                        <?= htmlspecialchars($error['errorType']) ?>
                                                    </span>
                                                </td>
                                                <td><?= htmlspecialchars($error['errorMessage']) ?></td>
                                                <td><?= htmlspecialchars($error['fieldName'] ?? '-') ?></td>
                                                <td>
                                                    <details>
                                                        <summary>View Data</summary>
                                                        <pre style="font-size: 0.75rem; margin-top: 0.5rem;"><?= htmlspecialchars($error['rowData']) ?></pre>
                                                    </details>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Skipped Records -->
                <?php if (!empty($skippedRecords)): ?>
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span style="margin-right: 0.5rem;">⏭️</span>
                                Skipped Records (<?= count($skippedRecords) ?>)
                            </h2>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Row</th>
                                            <th>Reason</th>
                                            <th>Details</th>
                                            <th>Row Data</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($skippedRecords as $record): ?>
                                            <tr>
                                                <td><?= $record['rowNumber'] ?></td>
                                                <td>
                                                    <span class="badge badge-warning">
                                                        <?= htmlspecialchars($record['reason']) ?>
                                                    </span>
                                                </td>
                                                <td><?= htmlspecialchars($record['details']) ?></td>
                                                <td>
                                                    <details>
                                                        <summary>View Data</summary>
                                                        <pre style="font-size: 0.75rem; margin-top: 0.5rem;"><?= htmlspecialchars($record['rowData']) ?></pre>
                                                    </details>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (empty($errors) && empty($skippedRecords)): ?>
                    <div class="card">
                        <div class="card-body text-center">
                            <h3>No Errors or Skipped Records</h3>
                            <p>This import job completed without any errors or skipped records.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <style>
        .badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .badge-error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        
        .badge-warning {
            background: #fffbeb;
            color: #92400e;
            border: 1px solid #fed7aa;
        }
        
        details summary {
            cursor: pointer;
            color: #3b82f6;
            font-size: 0.875rem;
        }
        
        details summary:hover {
            text-decoration: underline;
        }
    </style>

    <script src="<?= asset_url('js/admin.js') ?>"></script>
    <script>
        // Mobile Menu Functionality
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');

            sidebar.classList.toggle('open');
            overlay.classList.toggle('active');

            if (sidebar.classList.contains('open')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        function closeMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');

            sidebar.classList.remove('open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        document.addEventListener('DOMContentLoaded', () => {
            const mobileToggle = document.getElementById('mobile-menu-toggle');
            if (mobileToggle) {
                mobileToggle.addEventListener('click', toggleMobileMenu);
            }

            const mobileOverlay = document.getElementById('mobile-overlay');
            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', closeMobileMenu);
            }

            const mobileCloseBtn = document.getElementById('mobile-close-btn');
            if (mobileCloseBtn) {
                mobileCloseBtn.addEventListener('click', closeMobileMenu);
            }

            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth <= 1023) {
                        closeMobileMenu();
                    }
                });
            });
        });

        window.addEventListener('resize', () => {
            if (window.innerWidth > 1023) {
                closeMobileMenu();
            }
        });

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeMobileMenu();
            }
        });
    </script>
</body>
</html>
