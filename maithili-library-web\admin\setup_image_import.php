<?php
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/Database.php';

// Check if user is logged in as admin
require_admin();

$db = new Database();
$success = false;
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['setup_tables'])) {
    try {
        // Read and execute the SQL file
        $sqlFile = '../database/image_import_tables.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('SQL file not found: ' . $sqlFile);
        }
        
        $sql = file_get_contents($sqlFile);
        if (!$sql) {
            throw new Exception('Could not read SQL file');
        }
        
        // Split SQL into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        $db->beginTransaction();
        
        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^(SET|START|COMMIT)/', $statement)) {
                $db->query($statement);
            }
        }
        
        $db->commit();
        $success = true;
        
    } catch (Exception $e) {
        $db->rollback();
        $error = $e->getMessage();
    }
}

// Check if tables exist
$tablesExist = $db->tableExists('image_import_jobs') && $db->tableExists('image_import_files');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Image Import - मैथिली विकास कोष</title>
    <link rel="stylesheet" href="<?= asset_url('css/admin.css') ?>">
    <link rel="stylesheet" href="<?= asset_url('css/responsive.css') ?>">
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <?php include 'includes/sidebar.php'; ?>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <div class="admin-header">
                <div class="admin-header-content">
                    <div class="admin-header-left">
                        <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                            <span></span>
                            <span></span>
                            <span></span>
                        </button>
                        <h1 class="admin-title">Setup Image Import</h1>
                        <p class="admin-subtitle">मैथिली विकास कोष</p>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="admin-content">
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <span class="alert-icon">✅</span>
                        Image import tables created successfully! You can now use the image import feature.
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <span class="alert-icon">❌</span>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <span style="margin-right: 0.5rem;">🛠️</span>
                            Image Import Setup
                        </h2>
                        <p class="card-description">Setup database tables for bulk image import functionality</p>
                    </div>
                    <div class="card-body">
                        <?php if ($tablesExist): ?>
                            <div class="alert alert-info">
                                <span class="alert-icon">ℹ️</span>
                                Image import tables already exist. The image import feature is ready to use.
                            </div>
                            
                            <div class="form-actions">
                                <a href="import-images.php" class="btn btn-primary">
                                    <span class="btn-icon">🖼️</span>
                                    Go to Image Import
                                </a>
                                <a href="books.php" class="btn btn-secondary">
                                    <span class="btn-icon">📚</span>
                                    Back to Books
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-warning">
                                <span class="alert-icon">⚠️</span>
                                Image import tables do not exist. Click the button below to create them.
                            </div>
                            
                            <h3>What will be created:</h3>
                            <ul style="margin: 1rem 0; padding-left: 2rem; line-height: 1.6;">
                                <li><strong>image_import_jobs</strong> - Tracks image import jobs and their status</li>
                                <li><strong>image_import_files</strong> - Stores information about individual uploaded files</li>
                            </ul>
                            
                            <form method="POST" class="form">
                                <div class="form-actions">
                                    <button type="submit" name="setup_tables" class="btn btn-primary">
                                        <span class="btn-icon">🛠️</span>
                                        Create Image Import Tables
                                    </button>
                                    <a href="books.php" class="btn btn-secondary">
                                        <span class="btn-icon">📚</span>
                                        Back to Books
                                    </a>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Feature Overview -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <span style="margin-right: 0.5rem;">📖</span>
                            Image Import Features
                        </h2>
                    </div>
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1.5rem;">
                            <div style="background: #f0f9ff; padding: 1rem; border-radius: 0.5rem; border: 1px solid #bae6fd;">
                                <h4 style="color: #0369a1; margin: 0 0 0.5rem 0;">📤 Bulk Upload</h4>
                                <p style="color: #0369a1; font-size: 0.875rem; margin: 0;">Upload multiple book cover images at once with drag-and-drop support</p>
                            </div>
                            
                            <div style="background: #f0fdf4; padding: 1rem; border-radius: 0.5rem; border: 1px solid #bbf7d0;">
                                <h4 style="color: #166534; margin: 0 0 0.5rem 0;">🔍 Smart Matching</h4>
                                <p style="color: #166534; font-size: 0.875rem; margin: 0;">Automatically match images to books using filename patterns</p>
                            </div>
                            
                            <div style="background: #fefce8; padding: 1rem; border-radius: 0.5rem; border: 1px solid #fde047;">
                                <h4 style="color: #a16207; margin: 0 0 0.5rem 0;">⚙️ Auto Processing</h4>
                                <p style="color: #a16207; font-size: 0.875rem; margin: 0;">Images are automatically resized and compressed to ~30KB JPEG format</p>
                            </div>
                            
                            <div style="background: #fdf2f8; padding: 1rem; border-radius: 0.5rem; border: 1px solid #f9a8d4;">
                                <h4 style="color: #be185d; margin: 0 0 0.5rem 0;">📊 Progress Tracking</h4>
                                <p style="color: #be185d; font-size: 0.875rem; margin: 0;">Real-time progress tracking with detailed status reports</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <!-- Scripts -->
    <script src="<?= asset_url('js/admin.js') ?>"></script>
</body>
</html>
