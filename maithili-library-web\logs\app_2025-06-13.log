[2025-06-13 07:47:45] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: books
[2025-06-13 07:47:45] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: authors
[2025-06-13 07:47:45] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: users
[2025-06-13 07:48:03] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: books
[2025-06-13 07:48:03] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: authors
[2025-06-13 07:48:03] [ERROR] [admin@::1] Table exists check failed: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near '?' at line 1 Table: users
