<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Import Books from CSV
 */

require_once '../config/config.php';
require_once '../includes/Database.php';
require_once '../includes/functions.php';
require_once '../includes/CSVParser.php';
require_once '../includes/ImportHelper.php';

// Check if user is logged in and has import permission
require_admin();

// Initialize database
$db = new Database();

// Handle actions
$action = $_GET['action'] ?? 'upload';
$jobId = $_GET['job_id'] ?? null;
$message = '';
$error = '';

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'upload') {
    try {
        if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('No file uploaded or upload error occurred');
        }
        
        $uploadedFile = $_FILES['csv_file'];

        // Validate file extension first
        $extension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));
        if ($extension !== 'csv') {
            throw new Exception('Only CSV files are allowed');
        }

        // Validate file size
        if ($uploadedFile['size'] > 10 * 1024 * 1024) {
            throw new Exception('File size exceeds 10MB limit');
        }

        // Validate file exists and is readable
        if (!file_exists($uploadedFile['tmp_name']) || !is_readable($uploadedFile['tmp_name'])) {
            throw new Exception('Uploaded file is not accessible');
        }
        
        // Create uploads directory if it doesn't exist
        $uploadsDir = UPLOAD_DIR . 'imports/';
        if (!is_dir($uploadsDir)) {
            mkdir($uploadsDir, 0755, true);
        }
        
        // Move uploaded file
        $fileName = 'import_' . date('Y-m-d_H-i-s') . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $uploadedFile['name']);
        $filePath = $uploadsDir . $fileName;
        
        if (!move_uploaded_file($uploadedFile['tmp_name'], $filePath)) {
            throw new Exception('Failed to save uploaded file');
        }
        
        // Create import job record
        $jobId = 'imp' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
        $preprocess = isset($_POST['preprocess']) ? 1 : 0;
        $handleMissingTitle = $_POST['handleMissingTitle'] ?? 'generate_default';
        $missingTitlePrefix = $_POST['missingTitlePrefix'] ?? 'Untitled Book';

        $db->insert('import_jobs', [
            'id' => $jobId,
            'userId' => $_SESSION['admin_user_id'],
            'fileName' => $uploadedFile['name'],
            'filePath' => $filePath,
            'preprocess' => $preprocess,
            'handleMissingTitle' => $handleMissingTitle,
            'missingTitlePrefix' => $missingTitlePrefix,
            'status' => 'PENDING',
            'createdAt' => date('Y-m-d H:i:s'),
            'updatedAt' => date('Y-m-d H:i:s')
        ]);
        
        // Log import start
        log_activity("Started CSV import: {$uploadedFile['name']}", 'INFO');
        
        $message = 'File uploaded successfully. Import job created with ID: ' . $jobId;
        $action = 'status';
        
    } catch (Exception $e) {
        $error = 'Upload failed: ' . $e->getMessage();
        log_activity("Import upload error: " . $e->getMessage(), 'ERROR');
    }
}

// Handle manual processing trigger
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'process' && $jobId) {
    try {
        // Update job status to processing
        $db->update('import_jobs', [
            'status' => 'PROCESSING',
            'startTime' => date('Y-m-d H:i:s'),
            'updatedAt' => date('Y-m-d H:i:s')
        ], 'id = ?', [$jobId]);
        
        // Redirect to processing page
        header("Location: import-process.php?job_id=$jobId");
        exit;
        
    } catch (Exception $e) {
        $error = 'Failed to start processing: ' . $e->getMessage();
        log_activity("Import process start error: " . $e->getMessage(), 'ERROR');
    }
}

// Get import job data for status view
$importJob = null;
if ($action === 'status' && $jobId) {
    $importJob = $db->fetch("
        SELECT ij.*, u.name as user_name 
        FROM import_jobs ij 
        LEFT JOIN users u ON ij.userId = u.id 
        WHERE ij.id = ?
    ", [$jobId]);
    
    if (!$importJob) {
        $error = 'Import job not found';
        $action = 'upload';
    }
}

// Get recent import jobs for history
$recentJobs = [];
try {
    $recentJobs = $db->fetchAll("
        SELECT ij.*, u.name as user_name 
        FROM import_jobs ij 
        LEFT JOIN users u ON ij.userId = u.id 
        ORDER BY ij.createdAt DESC 
        LIMIT 10
    ");
} catch (Exception $e) {
    // Silently handle error
}

$page_title = 'Import Books - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="<?= asset_url('css/style.css') ?>">
    <link rel="stylesheet" href="<?= asset_url('css/admin.css') ?>">

    <!-- Favicon and App Icons -->
    <?= generate_favicon_tags() ?>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <button class="mobile-close-btn" id="mobile-close-btn">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <div class="sidebar-logo">
                    <img src="<?= icon_url(48) ?>" alt="<?= APP_NAME ?> Icon" class="sidebar-logo-image">
                </div>
                <h2 class="sidebar-title">Admin Panel</h2>
                <p class="sidebar-subtitle">मैथिली विकास कोष</p>
            </div>

            <!-- Mobile User Section -->
            <div class="mobile-user-section">
                <div class="mobile-user-info">
                    <div class="mobile-user-avatar">
                        <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                    </div>
                    <div class="mobile-user-details">
                        <span class="mobile-user-name"><?= htmlspecialchars($_SESSION['admin_name']) ?></span>
                        <span class="mobile-user-role">Administrator</span>
                    </div>
                </div>
                <a href="logout.php" class="mobile-logout-btn">
                    <span class="logout-icon">🚪</span>
                    Logout
                </a>
            </div>

            <!-- Navigation -->
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Library Management</div>
                    <div class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Dashboard
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="books.php" class="nav-link">
                            <span class="nav-icon">📚</span>
                            Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-books.php" class="nav-link active">
                            <span class="nav-icon">📥</span>
                            Import Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-images.php" class="nav-link">
                            <span class="nav-icon">🖼️</span>
                            Import Images
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="authors.php" class="nav-link">
                            <span class="nav-icon">✍️</span>
                            Authors
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="categories.php" class="nav-link">
                            <span class="nav-icon">📂</span>
                            Categories
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="publishers.php" class="nav-link">
                            <span class="nav-icon">🏢</span>
                            Publishers
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="languages.php" class="nav-link">
                            <span class="nav-icon">🌐</span>
                            Languages
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="subjects.php" class="nav-link">
                            <span class="nav-icon">📖</span>
                            Subjects
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="series.php" class="nav-link">
                            <span class="nav-icon">📑</span>
                            Book Series
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="locations.php" class="nav-link">
                            <span class="nav-icon">📍</span>
                            Locations
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="conditions.php" class="nav-link">
                            <span class="nav-icon">🔧</span>
                            Conditions
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="sources.php" class="nav-link">
                            <span class="nav-icon">🎁</span>
                            Sources
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Reports & Analytics</div>
                    <div class="nav-item">
                        <a href="reports.php" class="nav-link">
                            <span class="nav-icon">📈</span>
                            Collection Reports
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="analytics.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Analytics
                        </a>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <header class="admin-header">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="header-title">
                            <span class="title-icon">📥</span>
                            Import Books
                        </h1>
                        <p class="header-subtitle">Bulk import books from CSV files</p>
                    </div>
                    <div class="header-actions">
                        <div class="user-info">
                            <span class="welcome-text">Welcome, <?= htmlspecialchars($_SESSION['admin_name']) ?>!</span>
                            <div class="user-menu">
                                <div class="user-avatar">
                                    <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                                </div>
                            </div>
                        </div>
                        <a href="logout.php" class="btn btn-secondary btn-sm">
                            <span style="margin-right: 0.5rem;">🚪</span>
                            Logout
                        </a>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Breadcrumb -->
                <nav class="breadcrumb">
                    <a href="dashboard.php" class="breadcrumb-item">Dashboard</a>
                    <span class="breadcrumb-separator">›</span>
                    <span class="breadcrumb-item active">Import Books</span>
                </nav>

                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <span class="alert-icon">✅</span>
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <span class="alert-icon">❌</span>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <?php if ($action === 'upload'): ?>
                    <!-- Upload Form -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span style="margin-right: 0.5rem;">📥</span>
                                Upload CSV File
                            </h2>
                            <p class="card-description">Upload a CSV file to import books into the library</p>
                        </div>
                        <div class="card-body">
                            <form method="POST" enctype="multipart/form-data" class="form">
                                <input type="hidden" name="action" value="upload">
                                
                                <div class="form-group">
                                    <label for="csv_file" class="form-label">CSV File *</label>
                                    <input type="file" id="csv_file" name="csv_file" accept=".csv" required class="form-control">
                                    <div class="form-help">
                                        Maximum file size: 10MB. Only CSV files are supported.
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="preprocess" value="1" checked>
                                        <span class="checkbox-text">Enable preprocessing (recommended)</span>
                                    </label>
                                    <div class="form-help">
                                        Preprocessing includes data validation, fuzzy matching, and automatic entity creation.
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="handleMissingTitle" class="form-label">Missing Title Handling</label>
                                    <select id="handleMissingTitle" name="handleMissingTitle" class="form-control">
                                        <option value="generate_default">Generate default title (recommended)</option>
                                        <option value="use_accession">Use accession number as title</option>
                                        <option value="skip_row">Skip rows with missing titles</option>
                                    </select>
                                    <div class="form-help">
                                        Choose how to handle rows that don't have a title field.
                                    </div>
                                </div>

                                <div class="form-group" id="missingTitlePrefixGroup">
                                    <label for="missingTitlePrefix" class="form-label">Default Title Prefix</label>
                                    <input type="text" id="missingTitlePrefix" name="missingTitlePrefix" value="Untitled Book" class="form-control">
                                    <div class="form-help">
                                        Prefix to use when generating default titles for books without titles.
                                    </div>
                                </div>
                                
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <span class="btn-icon">📤</span>
                                        Upload and Import
                                    </button>
                                    <a href="books.php" class="btn btn-secondary">
                                        <span class="btn-icon">📚</span>
                                        Back to Books
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($action === 'status' && $importJob): ?>
                    <!-- Import Status -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span style="margin-right: 0.5rem;">📊</span>
                                Import Status
                            </h2>
                            <p class="card-description">Job ID: <?= htmlspecialchars($importJob['id']) ?></p>
                        </div>
                        <div class="card-body">
                            <div class="import-status">
                                <div class="status-grid">
                                    <div class="status-item">
                                        <span class="status-label">File Name:</span>
                                        <span class="status-value"><?= htmlspecialchars($importJob['fileName']) ?></span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Status:</span>
                                        <span class="status-value status-<?= strtolower($importJob['status']) ?>">
                                            <?= htmlspecialchars($importJob['status']) ?>
                                        </span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Created:</span>
                                        <span class="status-value"><?= date('Y-m-d H:i:s', strtotime($importJob['createdAt'])) ?></span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">User:</span>
                                        <span class="status-value"><?= htmlspecialchars($importJob['user_name'] ?? 'Unknown') ?></span>
                                    </div>
                                </div>

                                <?php if ($importJob['status'] === 'PENDING'): ?>
                                    <div class="status-actions">
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="process">
                                            <button type="submit" class="btn btn-primary">
                                                <span class="btn-icon">▶️</span>
                                                Start Processing
                                            </button>
                                        </form>
                                    </div>
                                <?php elseif ($importJob['status'] === 'PROCESSING'): ?>
                                    <div class="status-message">
                                        <span class="status-icon">⏳</span>
                                        Processing in progress... Please wait.
                                    </div>
                                    <script>
                                        // Auto-refresh every 5 seconds during processing
                                        setTimeout(function() {
                                            window.location.reload();
                                        }, 5000);
                                    </script>
                                <?php elseif ($importJob['status'] === 'COMPLETED'): ?>
                                    <div class="import-stats">
                                        <h3>Import Statistics</h3>
                                        <div class="stats-grid">
                                            <div class="stat-item success">
                                                <span class="stat-number"><?= $importJob['importedRows'] ?></span>
                                                <span class="stat-label">Books Imported</span>
                                            </div>
                                            <div class="stat-item warning">
                                                <span class="stat-number"><?= $importJob['skippedRows'] ?></span>
                                                <span class="stat-label">Rows Skipped</span>
                                            </div>
                                            <div class="stat-item error">
                                                <span class="stat-number"><?= $importJob['errorRows'] ?></span>
                                                <span class="stat-label">Errors</span>
                                            </div>
                                            <div class="stat-item info">
                                                <span class="stat-number"><?= $importJob['totalRows'] ?></span>
                                                <span class="stat-label">Total Rows</span>
                                            </div>
                                        </div>

                                        <div class="creation-stats">
                                            <h4>Entities Created</h4>
                                            <div class="creation-grid">
                                                <div class="creation-item">
                                                    <span class="creation-number"><?= $importJob['authorsCreated'] ?></span>
                                                    <span class="creation-label">Authors</span>
                                                </div>
                                                <div class="creation-item">
                                                    <span class="creation-number"><?= $importJob['categoriesCreated'] ?></span>
                                                    <span class="creation-label">Categories</span>
                                                </div>
                                                <div class="creation-item">
                                                    <span class="creation-number"><?= $importJob['publishersCreated'] ?></span>
                                                    <span class="creation-label">Publishers</span>
                                                </div>
                                                <div class="creation-item">
                                                    <span class="creation-number"><?= $importJob['languagesCreated'] ?></span>
                                                    <span class="creation-label">Languages</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Action Links -->
                                        <div class="import-actions" style="margin-top: 1.5rem; display: flex; gap: 1rem; flex-wrap: wrap;">
                                            <?php if ($importJob['errorRows'] > 0): ?>
                                                <a href="import-errors.php?job_id=<?= urlencode($importJob['id']) ?>" class="btn btn-sm btn-outline-danger">
                                                    <span style="margin-right: 0.25rem;">👁️</span>
                                                    View <?= $importJob['errorRows'] ?> Error<?= $importJob['errorRows'] !== 1 ? 's' : '' ?>
                                                </a>
                                            <?php endif; ?>

                                            <?php if ($importJob['skippedRows'] > 0): ?>
                                                <a href="import-errors.php?job_id=<?= urlencode($importJob['id']) ?>" class="btn btn-sm btn-outline-warning">
                                                    <span style="margin-right: 0.25rem;">👁️</span>
                                                    View <?= $importJob['skippedRows'] ?> Skipped
                                                </a>
                                            <?php endif; ?>

                                            <?php
                                            $totalFuzzyMatches = ($importJob['authorsFuzzyMatched'] ?? 0) +
                                                               ($importJob['categoriesFuzzyMatched'] ?? 0) +
                                                               ($importJob['publishersFuzzyMatched'] ?? 0) +
                                                               ($importJob['sourcesFuzzyMatched'] ?? 0);
                                            if ($totalFuzzyMatches > 0): ?>
                                                <a href="import-fuzzy-matches.php?job_id=<?= urlencode($importJob['id']) ?>" class="btn btn-sm btn-outline-success">
                                                    <span style="margin-right: 0.25rem;">🔍</span>
                                                    View <?= $totalFuzzyMatches ?> Fuzzy Match<?= $totalFuzzyMatches !== 1 ? 'es' : '' ?>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php elseif ($importJob['status'] === 'FAILED'): ?>
                                    <div class="status-error">
                                        <span class="status-icon">❌</span>
                                        <div class="error-details">
                                            <h4>Import Failed</h4>
                                            <?php if ($importJob['errorMessage']): ?>
                                                <p><?= htmlspecialchars($importJob['errorMessage']) ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="card-actions">
                                <a href="import-books.php" class="btn btn-secondary">
                                    <span class="btn-icon">📥</span>
                                    New Import
                                </a>
                                <a href="books.php" class="btn btn-primary">
                                    <span class="btn-icon">📚</span>
                                    View Books
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Import History -->
                <?php if (!empty($recentJobs)): ?>
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span style="margin-right: 0.5rem;">📋</span>
                                Recent Import Jobs
                            </h2>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Job ID</th>
                                            <th>File Name</th>
                                            <th>Status</th>
                                            <th>Imported</th>
                                            <th>Errors</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentJobs as $job): ?>
                                            <tr>
                                                <td>
                                                    <code><?= htmlspecialchars($job['id']) ?></code>
                                                </td>
                                                <td><?= htmlspecialchars($job['fileName']) ?></td>
                                                <td>
                                                    <span class="status-badge status-<?= strtolower($job['status']) ?>">
                                                        <?= htmlspecialchars($job['status']) ?>
                                                    </span>
                                                </td>
                                                <td><?= $job['importedRows'] ?></td>
                                                <td><?= $job['errorRows'] ?></td>
                                                <td><?= date('M j, Y H:i', strtotime($job['createdAt'])) ?></td>
                                                <td>
                                                    <a href="import-books.php?action=status&job_id=<?= urlencode($job['id']) ?>"
                                                       class="btn btn-sm btn-secondary">
                                                        View
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- CSV Format Guide -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <span style="margin-right: 0.5rem;">📖</span>
                            CSV Format Guide
                        </h2>
                    </div>
                    <div class="card-body">
                        <div class="format-guide">
                            <h3>Required Columns</h3>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1.5rem;">
                                <div style="background: #fef2f2; padding: 0.75rem; border-radius: 0.5rem; border: 1px solid #fecaca;">
                                    <span style="font-weight: 600; color: #991b1b;">Acc. No.</span>
                                    <p style="color: #dc2626; font-size: 0.875rem; margin: 0.25rem 0 0 0;">Accession number from your CSV</p>
                                </div>
                                <div style="background: #fef2f2; padding: 0.75rem; border-radius: 0.5rem; border: 1px solid #fecaca;">
                                    <span style="font-weight: 600; color: #991b1b;">Title</span>
                                    <p style="color: #dc2626; font-size: 0.875rem; margin: 0.25rem 0 0 0;">Book title</p>
                                </div>
                                <div style="background: #fef2f2; padding: 0.75rem; border-radius: 0.5rem; border: 1px solid #fecaca;">
                                    <span style="font-weight: 600; color: #991b1b;">Author</span>
                                    <p style="color: #dc2626; font-size: 0.875rem; margin: 0.25rem 0 0 0;">Author name</p>
                                </div>
                            </div>

                            <h3>Optional Columns</h3>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 0.5rem; margin-bottom: 1.5rem; font-size: 0.875rem; color: #6b7280;">
                                <span>Publisher</span>
                                <span>Published Year</span>
                                <span>Language</span>
                                <span>Category</span>
                                <span>Subject</span>
                                <span>Series</span>
                                <span>Location</span>
                                <span>Condition</span>
                                <span>Pages</span>
                                <span>Source</span>
                                <span>Price</span>
                                <span>Shelf</span>
                                <span>Row</span>
                                <span>ISBN</span>
                                <span>Edition</span>
                                <span>Remarks</span>
                            </div>

                            <div style="background: #eff6ff; padding: 1rem; border-radius: 0.5rem; border: 1px solid #dbeafe;">
                                <h3 style="font-weight: 600; color: #1e40af; margin: 0 0 0.5rem 0;">Tips</h3>
                                <ul style="font-size: 0.875rem; color: #1e40af; margin: 0; padding-left: 1.25rem; line-height: 1.5;">
                                    <li>• Ensure your CSV file has headers in the first row</li>
                                    <li>• Use UTF-8 encoding for proper Devanagari character support</li>
                                    <li>• <strong>Accession numbers from CSV are preserved exactly as provided</strong></li>
                                    <li>• MVKL format (MVKL001, MVKL100) will be used as-is</li>
                                    <li>• <strong>Devanagari numbers supported (MVKL१००, १७१) - automatically converted</strong></li>
                                    <li>• Other formats (LIB001, B123) will also be preserved</li>
                                    <li>• Numbers only (123, 456, १७१) will get MVKL prefix automatically</li>
                                    <li>• <strong>Bikram Sambat dates supported (२०७८, २०८०) - automatically converted to AD</strong></li>
                                    <li>• E.D. prefix for Gregorian dates (E.D. 2023) supported</li>
                                    <li>• <strong>Enhanced price formats (500/-, १००/-, USD 12.50 Dollar) supported</strong></li>
                                    <li>• Empty or invalid accession numbers will be auto-generated</li>
                                    <li>• Duplicate accession numbers will be skipped</li>
                                    <li>• <strong>All catalog entities will be created automatically if they don't exist</strong></li>
                                    <li>• Publishers, subjects, book series, locations, and conditions are auto-created</li>
                                    <li>• Sources will be intelligently categorized (Donor, Purchase, Government, etc.)</li>
                                    <li>• Subjects are automatically linked to their categories</li>
                                    <li>• Locations support section-shelf-row format (e.g., A-1-Top)</li>
                                    <li>• Conditions get appropriate colors based on their names</li>
                                    <li>• Enable preprocessing for files with formatting issues</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <!-- Scripts -->
    <script src="<?= asset_url('js/admin.js') ?>"></script>

    <script>
        // Mobile Menu Functionality
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');

            sidebar.classList.toggle('open');
            overlay.classList.toggle('active');

            // Prevent body scroll when menu is open
            if (sidebar.classList.contains('open')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        function closeMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');

            sidebar.classList.remove('open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            // Mobile menu toggle button event listener
            const mobileToggle = document.getElementById('mobile-menu-toggle');
            if (mobileToggle) {
                mobileToggle.addEventListener('click', toggleMobileMenu);
            }

            // Mobile overlay click event listener
            const mobileOverlay = document.getElementById('mobile-overlay');
            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', closeMobileMenu);
            }

            // Mobile close button event listener
            const mobileCloseBtn = document.getElementById('mobile-close-btn');
            if (mobileCloseBtn) {
                mobileCloseBtn.addEventListener('click', closeMobileMenu);
            }

            // Close mobile menu when clicking on nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth <= 1023) {
                        closeMobileMenu();
                    }
                });
            });

            // Add active class to current page nav link
            const currentPage = window.location.pathname.split('/').pop();
            const navLinks = document.querySelectorAll('.nav-link');

            navLinks.forEach(link => {
                const href = link.getAttribute('href');
                if (href && (href === currentPage || href.includes(currentPage))) {
                    link.classList.add('active');
                }
            });

            console.log('Import Books page loaded with responsive features');
        });

        // Close mobile menu on window resize if screen becomes large
        window.addEventListener('resize', () => {
            if (window.innerWidth > 1023) {
                closeMobileMenu();
            }
        });

        // Handle escape key to close mobile menu
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeMobileMenu();
            }
        });

        // Auto-hide mobile menu on scroll
        let lastScrollTop = 0;
        window.addEventListener('scroll', () => {
            const currentScroll = window.pageYOffset || document.documentElement.scrollTop;

            if (currentScroll > lastScrollTop && currentScroll > 100) {
                // Scrolling down - hide mobile menu
                if (window.innerWidth <= 1023) {
                    closeMobileMenu();
                }
            }

            lastScrollTop = currentScroll <= 0 ? 0 : currentScroll;
        });
    </script>

    <style>
        .import-status {
            margin-bottom: 2rem;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .status-item {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .status-label {
            font-size: 0.875rem;
            color: var(--text-light);
            font-weight: 500;
        }

        .status-value {
            font-weight: 600;
            color: var(--text-dark);
        }

        .status-pending { color: #f59e0b; }
        .status-processing { color: #3b82f6; }
        .status-completed { color: #10b981; }
        .status-failed { color: #ef4444; }

        .status-actions {
            margin-top: 1rem;
        }

        .status-message {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            background: #f3f4f6;
            border-radius: 0.5rem;
            margin-top: 1rem;
        }

        .import-stats {
            margin-top: 1.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .stat-item {
            text-align: center;
            padding: 1rem;
            border-radius: 0.5rem;
            border: 2px solid;
        }

        .stat-item.success { border-color: #10b981; background: #ecfdf5; }
        .stat-item.warning { border-color: #f59e0b; background: #fffbeb; }
        .stat-item.error { border-color: #ef4444; background: #fef2f2; }
        .stat-item.info { border-color: #3b82f6; background: #eff6ff; }

        .stat-number {
            display: block;
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-light);
        }

        .creation-stats {
            margin-top: 1.5rem;
        }

        .creation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 0.75rem;
            margin-top: 0.75rem;
        }

        .creation-item {
            text-align: center;
            padding: 0.75rem;
            background: #f9fafb;
            border-radius: 0.375rem;
        }

        .creation-number {
            display: block;
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .creation-label {
            font-size: 0.75rem;
            color: var(--text-light);
        }

        .status-error {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            padding: 1rem;
            background: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 0.5rem;
            margin-top: 1rem;
        }

        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .format-guide h3 {
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
            color: var(--primary-color);
        }

        .format-list {
            margin-left: 1rem;
            margin-bottom: 1rem;
        }

        .format-list li {
            margin-bottom: 0.5rem;
        }

        .btn-outline-danger {
            color: #dc2626;
            border: 1px solid #dc2626;
            background: transparent;
        }

        .btn-outline-danger:hover {
            background: #dc2626;
            color: white;
        }

        .btn-outline-warning {
            color: #d97706;
            border: 1px solid #d97706;
            background: transparent;
        }

        .btn-outline-warning:hover {
            background: #d97706;
            color: white;
        }

        .btn-outline-success {
            color: #059669;
            border: 1px solid #059669;
            background: transparent;
        }

        .btn-outline-success:hover {
            background: #059669;
            color: white;
        }
    </style>

    <!-- Scripts -->
    <script src="<?= asset_url('js/admin.js') ?>"></script>
    <script>
        // Handle missing title options
        document.addEventListener('DOMContentLoaded', function() {
            const handleMissingTitleSelect = document.getElementById('handleMissingTitle');
            const missingTitlePrefixGroup = document.getElementById('missingTitlePrefixGroup');

            function togglePrefixField() {
                if (handleMissingTitleSelect.value === 'generate_default') {
                    missingTitlePrefixGroup.style.display = 'block';
                } else {
                    missingTitlePrefixGroup.style.display = 'none';
                }
            }

            handleMissingTitleSelect.addEventListener('change', togglePrefixField);
            togglePrefixField(); // Initial call
        });
    </script>
</body>
</html>
