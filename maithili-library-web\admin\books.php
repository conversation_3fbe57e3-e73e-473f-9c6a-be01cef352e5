<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Books Management
 */

require_once '../config/config.php';
require_once '../includes/Database.php';
require_once '../includes/functions.php';

// Check if user is logged in
require_admin();

// Initialize database
$db = new Database();

// Handle actions
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? null;
$message = '';
$error = '';
$image_upload_error = null;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
            case 'edit':
                // Get form data
                $accessionNo = trim($_POST['accessionNo'] ?? '');
                $title = trim($_POST['title'] ?? '');
                $titleNepali = trim($_POST['titleNepali'] ?? '') ?: null;
                $authorId = trim($_POST['authorId'] ?? '');
                $publisherId = trim($_POST['publisherId'] ?? '') ?: null;
                $publisher = trim($_POST['publisher'] ?? '') ?: null;
                $publishedYear = intval($_POST['publishedYear'] ?? 0);
                $languageId = trim($_POST['languageId'] ?? '');
                $categoryId = trim($_POST['categoryId'] ?? '');
                $subjectId = trim($_POST['subjectId'] ?? '') ?: null;
                $seriesId = trim($_POST['seriesId'] ?? '') ?: null;
                $locationId = trim($_POST['locationId'] ?? '') ?: null;
                $conditionId = trim($_POST['conditionId'] ?? '') ?: null;
                $pages = intval($_POST['pages'] ?? 0) ?: null;
                $sourceId = trim($_POST['sourceId'] ?? '') ?: null;
                $price = floatval($_POST['price'] ?? 0) ?: null;
                $originalPrice = floatval($_POST['originalPrice'] ?? 0) ?: null;
                $originalCurrency = trim($_POST['originalCurrency'] ?? 'NPR');
                $bookNo = trim($_POST['bookNo'] ?? '') ?: null;
                $billNoAndDate = trim($_POST['billNoAndDate'] ?? '') ?: null;
                $shelf = trim($_POST['shelf'] ?? '') ?: null;
                $row = trim($_POST['row'] ?? '') ?: null;
                $remark = trim($_POST['remark'] ?? '') ?: null;
                $dateReceived = trim($_POST['dateReceived'] ?? '') ?: null;
                $isbn = trim($_POST['isbn'] ?? '') ?: null;
                $edition = trim($_POST['edition'] ?? '') ?: null;
                $isAvailable = isset($_POST['isAvailable']) ? 1 : 0;

                // Handle image upload
                $image_upload_error = null;
                
                // Validation
                $validation_errors = [];
                
                if (empty($accessionNo)) {
                    $validation_errors[] = 'Accession number is required';
                } elseif (!preg_match('/^MVKL\d+(-\d+)?$/', $accessionNo)) {
                    $validation_errors[] = 'Accession number must start with MVKL followed by numbers (e.g., MVKL1, MVKL001, MVKL1-01)';
                }
                
                if (empty($title)) {
                    $validation_errors[] = 'Book title is required';
                }
                
                if (empty($authorId)) {
                    $validation_errors[] = 'Author is required';
                }
                
                if (empty($languageId)) {
                    $validation_errors[] = 'Language is required';
                }
                
                if (empty($categoryId)) {
                    $validation_errors[] = 'Category is required';
                }
                
                if ($publishedYear < 1000 || $publishedYear > date('Y') + 1) {
                    $validation_errors[] = 'Published year must be between 1000 and ' . (date('Y') + 1);
                }
                
                if (!empty($validation_errors)) {
                    $error = implode('<br>', $validation_errors);
                } else {
                    // Handle image upload if provided
                    if (isset($_FILES['book_image']) && $_FILES['book_image']['error'] === UPLOAD_ERR_OK) {
                        $book_id_for_image = $_POST['action'] === 'add' ? 'book' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT) : $_POST['id'];
                        $image_result = upload_book_image($_FILES['book_image'], $book_id_for_image);

                        if (!$image_result['success']) {
                            $image_upload_error = $image_result['error'];
                        }
                    }
                    try {
                        if ($_POST['action'] === 'add') {
                            // Check if accession number already exists
                            $existing = $db->fetch("SELECT id FROM books WHERE accessionNo = ? AND isDeleted = 0", [$accessionNo]);
                            if ($existing) {
                                $error = 'Book with this accession number already exists';
                            } else {
                                $book_id = $_POST['action'] === 'add' ? ($book_id_for_image ?? 'book' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT)) : $_POST['id'];
                                $data = [
                                    'id' => $book_id,
                                    'accessionNo' => $accessionNo,
                                    'title' => $title,
                                    'titleNepali' => $titleNepali,
                                    'authorId' => $authorId,
                                    'publisherId' => $publisherId,
                                    'publisher' => $publisher,
                                    'publishedYear' => $publishedYear,
                                    'languageId' => $languageId,
                                    'categoryId' => $categoryId,
                                    'subjectId' => $subjectId,
                                    'seriesId' => $seriesId,
                                    'locationId' => $locationId,
                                    'conditionId' => $conditionId,
                                    'pages' => $pages,
                                    'sourceId' => $sourceId,
                                    'price' => $price,
                                    'originalPrice' => $originalPrice,
                                    'originalCurrency' => $originalCurrency,
                                    'bookNo' => $bookNo,
                                    'billNoAndDate' => $billNoAndDate,
                                    'shelf' => $shelf,
                                    'row' => $row,
                                    'remark' => $remark,
                                    'dateReceived' => $dateReceived ? date('Y-m-d H:i:s', strtotime($dateReceived)) : null,
                                    'isbn' => $isbn,
                                    'edition' => $edition,
                                    'isAvailable' => $isAvailable,
                                    'createdAt' => date('Y-m-d H:i:s'),
                                    'updatedAt' => date('Y-m-d H:i:s')
                                ];
                                $db->insert('books', $data);
                                $message = 'Book added successfully';
                                log_activity("Added book: $title (Accession: $accessionNo)", 'INFO');
                                $action = 'list';
                            }
                        } else {
                            // Edit existing book
                            $edit_id = $_POST['id'] ?? '';
                            if ($edit_id) {
                                // Check if accession number conflicts with other books
                                $existing = $db->fetch("SELECT id FROM books WHERE accessionNo = ? AND id != ? AND isDeleted = 0", [$accessionNo, $edit_id]);
                                if ($existing) {
                                    $error = 'Book with this accession number already exists';
                                } else {
                                    $data = [
                                        'accessionNo' => $accessionNo,
                                        'title' => $title,
                                        'titleNepali' => $titleNepali,
                                        'authorId' => $authorId,
                                        'publisherId' => $publisherId,
                                        'publisher' => $publisher,
                                        'publishedYear' => $publishedYear,
                                        'languageId' => $languageId,
                                        'categoryId' => $categoryId,
                                        'subjectId' => $subjectId,
                                        'seriesId' => $seriesId,
                                        'locationId' => $locationId,
                                        'conditionId' => $conditionId,
                                        'pages' => $pages,
                                        'sourceId' => $sourceId,
                                        'price' => $price,
                                        'originalPrice' => $originalPrice,
                                        'originalCurrency' => $originalCurrency,
                                        'bookNo' => $bookNo,
                                        'billNoAndDate' => $billNoAndDate,
                                        'shelf' => $shelf,
                                        'row' => $row,
                                        'remark' => $remark,
                                        'dateReceived' => $dateReceived ? date('Y-m-d H:i:s', strtotime($dateReceived)) : null,
                                        'isbn' => $isbn,
                                        'edition' => $edition,
                                        'isAvailable' => $isAvailable,
                                        'updatedAt' => date('Y-m-d H:i:s')
                                    ];
                                    $db->update('books', $data, 'id = ?', [$edit_id]);
                                    $message = 'Book updated successfully';
                                    log_activity("Updated book: $title (Accession: $accessionNo)", 'INFO');
                                    $action = 'list';
                                }
                            }
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                        log_activity("Book save error: " . $e->getMessage(), 'ERROR');
                    }
                }
                break;
                
            case 'delete':
                $delete_id = $_POST['id'] ?? '';
                if ($delete_id) {
                    try {
                        $book_info = $db->fetch("SELECT title, accessionNo FROM books WHERE id = ?", [$delete_id]);
                        $db->delete('books', 'id = ?', [$delete_id]);
                        $message = 'Book deleted successfully';
                        log_activity("Deleted book: {$book_info['title']} (Accession: {$book_info['accessionNo']})", 'INFO');
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                        log_activity("Book delete error: " . $e->getMessage(), 'ERROR');
                    }
                }
                break;
        }
    }
}

// Handle AJAX delete request
if (isset($_GET['ajax_delete']) && $_GET['ajax_delete'] === '1' && $id) {
    header('Content-Type: application/json');
    try {
        $book_info = $db->fetch("SELECT title, accessionNo FROM books WHERE id = ?", [$id]);
        $db->delete('books', 'id = ?', [$id]);
        log_activity("Deleted book: {$book_info['title']} (Accession: {$book_info['accessionNo']})", 'INFO');
        echo json_encode(['success' => true, 'message' => 'Book deleted successfully']);
    } catch (Exception $e) {
        log_activity("Book delete error: " . $e->getMessage(), 'ERROR');
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
    exit;
}

// Handle AJAX accession number check
if (isset($_GET['check_accession']) && isset($_GET['accession_no'])) {
    header('Content-Type: application/json');
    $accession_no = trim($_GET['accession_no']);
    $exclude_id = $_GET['exclude_id'] ?? '';
    
    try {
        $where_clause = "accessionNo = ? AND isDeleted = 0";
        $params = [$accession_no];
        
        if ($exclude_id) {
            $where_clause .= " AND id != ?";
            $params[] = $exclude_id;
        }
        
        $existing = $db->fetch("SELECT id, title FROM books WHERE $where_clause", $params);
        
        if ($existing) {
            echo json_encode([
                'available' => false,
                'message' => 'Accession number already exists',
                'existing_book' => $existing['title']
            ]);
        } else {
            echo json_encode([
                'available' => true,
                'message' => 'Accession number is available'
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'available' => false,
            'message' => 'Error checking accession number'
        ]);
    }
    exit;
}

// Get data for edit form
$book_data = null;
if ($action === 'edit' && $id) {
    $book_data = $db->fetch("
        SELECT b.*,
               a.name as author_name,
               c.name as category_name,
               l.name as language_name,
               p.name as publisher_name
        FROM books b
        LEFT JOIN authors a ON b.authorId = a.id
        LEFT JOIN categories c ON b.categoryId = c.id
        LEFT JOIN languages l ON b.languageId = l.id
        LEFT JOIN publishers p ON b.publisherId = p.id
        WHERE b.id = ? AND b.isDeleted = 0
    ", [$id]);

    if (!$book_data) {
        $error = 'Book not found';
        $action = 'list';
    }
}

// Get dropdown data for forms
$authors = [];
$categories = [];
$languages = [];
$publishers = [];
$subjects = [];
$series = [];
$locations = [];
$conditions = [];
$sources = [];

if ($action === 'add' || $action === 'edit') {
    try {
        $authors = $db->fetchAll("SELECT id, name, nameNepali FROM authors WHERE isDeleted = 0 ORDER BY name ASC");
        $categories = $db->fetchAll("SELECT id, name, nameNepali FROM categories WHERE isDeleted = 0 ORDER BY name ASC");
        $languages = $db->fetchAll("SELECT id, name, nameNepali FROM languages WHERE isDeleted = 0 ORDER BY name ASC");
        $publishers = $db->fetchAll("SELECT id, name, nameNepali FROM publishers WHERE isDeleted = 0 ORDER BY name ASC");
        $subjects = $db->fetchAll("SELECT id, name, nameNepali, categoryId FROM subjects WHERE isDeleted = 0 ORDER BY name ASC");
        $series = $db->fetchAll("SELECT id, name, nameNepali FROM book_series WHERE isDeleted = 0 ORDER BY name ASC");
        $locations = $db->fetchAll("SELECT id, shelf, row, description FROM locations WHERE isDeleted = 0 ORDER BY shelf ASC, row ASC");
        $conditions = $db->fetchAll("SELECT id, name, description, color FROM conditions ORDER BY sortOrder ASC");
        $sources = $db->fetchAll("SELECT id, name, nameNepali, type FROM sources WHERE isDeleted = 0 ORDER BY name ASC");
    } catch (Exception $e) {
        $error = 'Error loading form data: ' . $e->getMessage();
        log_activity("Books form data error: " . $e->getMessage(), 'ERROR');
    }
}

// Get books list for list view
$books = [];
$total_books = 0;
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = ADMIN_ITEMS_PER_PAGE;
$search = trim($_GET['search'] ?? '');
$filter_author = trim($_GET['filter_author'] ?? '');
$filter_category = trim($_GET['filter_category'] ?? '');
$filter_language = trim($_GET['filter_language'] ?? '');
$filter_publisher = trim($_GET['filter_publisher'] ?? '');
$filter_available = $_GET['filter_available'] ?? '';
$sort_by = $_GET['sort_by'] ?? 'title';
$sort_order = $_GET['sort_order'] ?? 'ASC';

if ($action === 'list') {
    try {
        $where_conditions = ['b.isDeleted = 0'];
        $params = [];

        if (!empty($search)) {
            $where_conditions[] = '(b.title LIKE ? OR b.titleNepali LIKE ? OR b.accessionNo LIKE ? OR a.name LIKE ? OR b.isbn LIKE ?)';
            $search_param = "%$search%";
            $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param, $search_param]);
        }

        if (!empty($filter_author)) {
            $where_conditions[] = 'b.authorId = ?';
            $params[] = $filter_author;
        }

        if (!empty($filter_category)) {
            $where_conditions[] = 'b.categoryId = ?';
            $params[] = $filter_category;
        }

        if (!empty($filter_language)) {
            $where_conditions[] = 'b.languageId = ?';
            $params[] = $filter_language;
        }

        if (!empty($filter_publisher)) {
            $where_conditions[] = 'b.publisherId = ?';
            $params[] = $filter_publisher;
        }

        if ($filter_available !== '') {
            $where_conditions[] = 'b.isAvailable = ?';
            $params[] = intval($filter_available);
        }

        $where_clause = implode(' AND ', $where_conditions);

        // Validate sort parameters
        $allowed_sort_fields = ['title', 'accessionNo', 'publishedYear', 'createdAt', 'author_name', 'category_name'];
        if (!in_array($sort_by, $allowed_sort_fields)) {
            $sort_by = 'title';
        }
        $sort_order = strtoupper($sort_order) === 'DESC' ? 'DESC' : 'ASC';

        // Get total count
        $total_books = $db->fetchColumn("
            SELECT COUNT(*)
            FROM books b
            LEFT JOIN authors a ON b.authorId = a.id
            WHERE $where_clause
        ", $params);

        // Get books with related data
        $offset = ($page - 1) * $per_page;

        // Add LIMIT and OFFSET parameters
        $params[] = $per_page;
        $params[] = $offset;

        // Build the complete SQL query with safe interpolation
        // Handle special sorting for accession numbers to ensure proper numeric ordering
        $order_clause = "";
        if ($sort_by === 'accessionNo') {
            // Use natural sorting for accession numbers to handle numeric parts correctly
            // This will sort MVKL1, MVKL2, MVKL10, MVKL100 in proper numeric order
            $order_clause = "ORDER BY
                CAST(SUBSTRING(b.accessionNo, 5) AS UNSIGNED) $sort_order,
                b.accessionNo $sort_order";
        } else {
            $order_clause = "ORDER BY `$sort_by` $sort_order";
        }

        $sql = "
            SELECT b.*,
                   a.name as author_name,
                   a.nameNepali as author_nameNepali,
                   c.name as category_name,
                   c.nameNepali as category_nameNepali,
                   l.name as language_name,
                   l.nameNepali as language_nameNepali,
                   p.name as publisher_name,
                   p.nameNepali as publisher_nameNepali,
                   s.name as subject_name,
                   ser.name as series_name,
                   loc.shelf, loc.row,
                   cond.name as condition_name, cond.color as condition_color,
                   src.name as source_name
            FROM books b
            LEFT JOIN authors a ON b.authorId = a.id
            LEFT JOIN categories c ON b.categoryId = c.id
            LEFT JOIN languages l ON b.languageId = l.id
            LEFT JOIN publishers p ON b.publisherId = p.id
            LEFT JOIN subjects s ON b.subjectId = s.id
            LEFT JOIN book_series ser ON b.seriesId = ser.id
            LEFT JOIN locations loc ON b.locationId = loc.id
            LEFT JOIN conditions cond ON b.conditionId = cond.id
            LEFT JOIN sources src ON b.sourceId = src.id
            WHERE $where_clause
            $order_clause
            LIMIT ? OFFSET ?
        ";

        $books = $db->fetchAll($sql, $params);

    } catch (Exception $e) {
        $error = 'Database error: ' . $e->getMessage();
        log_activity("Books list error: " . $e->getMessage(), 'ERROR');
    }
}

// Get filter options for dropdowns
$filter_authors = [];
$filter_categories = [];
$filter_languages = [];
$filter_publishers = [];

if ($action === 'list') {
    try {
        $filter_authors = $db->fetchAll("SELECT DISTINCT a.id, a.name FROM authors a INNER JOIN books b ON a.id = b.authorId WHERE a.isDeleted = 0 AND b.isDeleted = 0 ORDER BY a.name ASC");
        $filter_categories = $db->fetchAll("SELECT DISTINCT c.id, c.name FROM categories c INNER JOIN books b ON c.id = b.categoryId WHERE c.isDeleted = 0 AND b.isDeleted = 0 ORDER BY c.name ASC");
        $filter_languages = $db->fetchAll("SELECT DISTINCT l.id, l.name FROM languages l INNER JOIN books b ON l.id = b.languageId WHERE l.isDeleted = 0 AND b.isDeleted = 0 ORDER BY l.name ASC");
        $filter_publishers = $db->fetchAll("SELECT DISTINCT p.id, p.name FROM publishers p INNER JOIN books b ON p.id = b.publisherId WHERE p.isDeleted = 0 AND b.isDeleted = 0 ORDER BY p.name ASC");
    } catch (Exception $e) {
        // Silently handle filter options error
    }
}

$page_title = 'Books Management - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>

    <!-- Stylesheets -->
    <link rel="stylesheet" href="<?= asset_url('css/style.css') ?>">
    <link rel="stylesheet" href="<?= asset_url('css/admin.css') ?>">

    <!-- Favicon and App Icons -->
    <?= generate_favicon_tags() ?>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <button class="mobile-close-btn" id="mobile-close-btn">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <div class="sidebar-logo">
                    <img src="<?= icon_url(48) ?>" alt="<?= APP_NAME ?> Icon" class="sidebar-logo-image">
                </div>
                <h2 class="sidebar-title">Admin Panel</h2>
                <p class="sidebar-subtitle">मैथिली विकास कोष</p>
            </div>

            <!-- Mobile User Section -->
            <div class="mobile-user-section">
                <div class="mobile-user-info">
                    <div class="mobile-user-avatar">
                        <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                    </div>
                    <div class="mobile-user-details">
                        <span class="mobile-user-name"><?= htmlspecialchars($_SESSION['admin_name']) ?></span>
                        <span class="mobile-user-role">Administrator</span>
                    </div>
                </div>
                <a href="logout.php" class="mobile-logout-btn">
                    <span class="logout-icon">🚪</span>
                    Logout
                </a>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <div class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Dashboard
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Book Management</div>
                    <div class="nav-item">
                        <a href="books.php" class="nav-link <?= $action === 'list' ? 'active' : '' ?>">
                            <span class="nav-icon">📚</span>
                            Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="books.php?action=add" class="nav-link <?= $action === 'add' ? 'active' : '' ?>">
                            <span class="nav-icon">➕</span>
                            Add Book
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-books.php" class="nav-link">
                            <span class="nav-icon">📥</span>
                            Import Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-images.php" class="nav-link">
                            <span class="nav-icon">🖼️</span>
                            Import Images
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Catalog Management</div>
                    <div class="nav-item">
                        <a href="authors.php" class="nav-link">
                            <span class="nav-icon">✍️</span>
                            Authors
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="categories.php" class="nav-link">
                            <span class="nav-icon">🏷️</span>
                            Categories
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="publishers.php" class="nav-link">
                            <span class="nav-icon">🏢</span>
                            Publishers
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="languages.php" class="nav-link">
                            <span class="nav-icon">🌐</span>
                            Languages
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="subjects.php" class="nav-link">
                            <span class="nav-icon">📖</span>
                            Subjects
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="series.php" class="nav-link">
                            <span class="nav-icon">📑</span>
                            Book Series
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="locations.php" class="nav-link">
                            <span class="nav-icon">📍</span>
                            Locations
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="conditions.php" class="nav-link">
                            <span class="nav-icon">🔧</span>
                            Conditions
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="sources.php" class="nav-link">
                            <span class="nav-icon">🎁</span>
                            Sources
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Reports & Analytics</div>
                    <div class="nav-item">
                        <a href="reports.php" class="nav-link">
                            <span class="nav-icon">📈</span>
                            Collection Reports
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="analytics.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Analytics
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="contact_inquiries.php" class="nav-link">
                            <span class="nav-icon">💬</span>
                            Contact Inquiries
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <div class="nav-item">
                        <a href="users.php" class="nav-link">
                            <span class="nav-icon">👤</span>
                            Admin Users
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="settings.php" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            Settings
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="backup.php" class="nav-link">
                            <span class="nav-icon">💾</span>
                            Backup & Export
                        </a>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <header class="admin-header">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="header-title">
                            <span class="title-icon">📚</span>
                            Books Management
                        </h1>
                        <p class="header-subtitle">Manage your library's book collection</p>
                    </div>
                    <div class="header-actions">
                        <div class="user-info">
                            <span class="welcome-text">Welcome, <?= htmlspecialchars($_SESSION['admin_name']) ?>!</span>
                            <div class="user-menu">
                                <div class="user-avatar">
                                    <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                                </div>
                            </div>
                        </div>
                        <a href="logout.php" class="btn btn-secondary btn-sm">
                            <span style="margin-right: 0.5rem;">🚪</span>
                            Logout
                        </a>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Breadcrumb -->
                <nav class="breadcrumb">
                    <a href="dashboard.php" class="breadcrumb-item">Dashboard</a>
                    <span class="breadcrumb-separator">›</span>
                    <?php if ($action === 'add'): ?>
                        <a href="books.php" class="breadcrumb-item">Books</a>
                        <span class="breadcrumb-separator">›</span>
                        <span class="breadcrumb-item active">Add Book</span>
                    <?php elseif ($action === 'edit'): ?>
                        <a href="books.php" class="breadcrumb-item">Books</a>
                        <span class="breadcrumb-separator">›</span>
                        <span class="breadcrumb-item active">Edit Book</span>
                    <?php else: ?>
                        <span class="breadcrumb-item active">Books</span>
                    <?php endif; ?>
                </nav>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <span class="alert-icon">✅</span>
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <span class="alert-icon">❌</span>
                        <?= $error ?>
                    </div>
                <?php endif; ?>

                <?php if ($action === 'list'): ?>
                    <!-- Books List -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <h2 class="page-title">Books Collection</h2>
                            <p class="page-subtitle">Manage your library's book collection</p>
                        </div>
                        <div class="page-header-actions">
                            <a href="?action=add" class="btn btn-primary">
                                <span style="margin-right: 0.5rem;">➕</span>
                                Add Book
                            </a>
                        </div>
                    </div>

                    <!-- Search and Filters -->
                    <div class="card">
                        <div class="card-body">
                            <form method="GET" class="search-form">
                                <div class="search-form-row">
                                    <div class="search-input-group">
                                        <input type="text" name="search" value="<?= htmlspecialchars($search) ?>"
                                               placeholder="Search books by title, author, accession number, ISBN..." class="input">
                                        <button type="submit" class="btn btn-secondary">
                                            <span style="margin-right: 0.5rem;">🔍</span>
                                            Search
                                        </button>
                                    </div>
                                    <?php if (!empty($search) || !empty($filter_author) || !empty($filter_category) || !empty($filter_language) || !empty($filter_publisher) || $filter_available !== ''): ?>
                                        <a href="?" class="btn btn-outline">Clear Filters</a>
                                    <?php endif; ?>
                                </div>

                                <!-- Advanced Filters -->
                                <div class="search-filters">
                                    <div class="filter-row">
                                        <select name="filter_author" class="input">
                                            <option value="">All Authors</option>
                                            <?php foreach ($filter_authors as $author): ?>
                                                <option value="<?= htmlspecialchars($author['id']) ?>" <?= $filter_author === $author['id'] ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($author['name']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>

                                        <select name="filter_category" class="input">
                                            <option value="">All Categories</option>
                                            <?php foreach ($filter_categories as $category): ?>
                                                <option value="<?= htmlspecialchars($category['id']) ?>" <?= $filter_category === $category['id'] ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($category['name']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>

                                        <select name="filter_language" class="input">
                                            <option value="">All Languages</option>
                                            <?php foreach ($filter_languages as $language): ?>
                                                <option value="<?= htmlspecialchars($language['id']) ?>" <?= $filter_language === $language['id'] ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($language['name']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>

                                        <select name="filter_publisher" class="input">
                                            <option value="">All Publishers</option>
                                            <?php foreach ($filter_publishers as $publisher): ?>
                                                <option value="<?= htmlspecialchars($publisher['id']) ?>" <?= $filter_publisher === $publisher['id'] ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($publisher['name']) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>

                                        <select name="filter_available" class="input">
                                            <option value="">All Status</option>
                                            <option value="1" <?= $filter_available === '1' ? 'selected' : '' ?>>Available</option>
                                            <option value="0" <?= $filter_available === '0' ? 'selected' : '' ?>>Not Available</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Sort Options -->
                                <div class="sort-options">
                                    <select name="sort_by" class="input">
                                        <option value="title" <?= $sort_by === 'title' ? 'selected' : '' ?>>Sort by Title</option>
                                        <option value="accessionNo" <?= $sort_by === 'accessionNo' ? 'selected' : '' ?>>Sort by Accession No.</option>
                                        <option value="author_name" <?= $sort_by === 'author_name' ? 'selected' : '' ?>>Sort by Author</option>
                                        <option value="category_name" <?= $sort_by === 'category_name' ? 'selected' : '' ?>>Sort by Category</option>
                                        <option value="publishedYear" <?= $sort_by === 'publishedYear' ? 'selected' : '' ?>>Sort by Year</option>
                                        <option value="createdAt" <?= $sort_by === 'createdAt' ? 'selected' : '' ?>>Sort by Date Added</option>
                                    </select>

                                    <select name="sort_order" class="input">
                                        <option value="ASC" <?= $sort_order === 'ASC' ? 'selected' : '' ?>>Ascending</option>
                                        <option value="DESC" <?= $sort_order === 'DESC' ? 'selected' : '' ?>>Descending</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Books Table -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                Books List
                                <?php if (!empty($search)): ?>
                                    <span class="text-muted">(Search: "<?= htmlspecialchars($search) ?>")</span>
                                <?php endif; ?>
                            </h3>
                            <div class="card-header-actions">
                                <span class="text-muted">Total: <?= number_format($total_books) ?> books</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($books)): ?>
                                <div class="empty-state">
                                    <div class="empty-state-icon">📚</div>
                                    <h3 class="empty-state-title">No Books Found</h3>
                                    <p class="empty-state-description">
                                        <?php if (!empty($search) || !empty($filter_author) || !empty($filter_category)): ?>
                                            No books match your search criteria. Try adjusting your search terms or filters.
                                        <?php else: ?>
                                            Start by adding your first book to the library collection.
                                        <?php endif; ?>
                                    </p>
                                    <div class="empty-state-actions">
                                        <?php if (!empty($search) || !empty($filter_author) || !empty($filter_category)): ?>
                                            <a href="?" class="btn btn-secondary">Clear Filters</a>
                                        <?php endif; ?>
                                        <a href="?action=add" class="btn btn-primary">Add First Book</a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Cover</th>
                                                <th class="sortable">
                                                    <?php
                                                    $current_params = $_GET;
                                                    $current_params['sort_by'] = 'accessionNo';
                                                    $new_order = ($sort_by === 'accessionNo' && $sort_order === 'ASC') ? 'DESC' : 'ASC';
                                                    $current_params['sort_order'] = $new_order;
                                                    $sort_url = '?' . http_build_query($current_params);
                                                    ?>
                                                    <a href="<?= $sort_url ?>" class="sort-link">
                                                        Accession No.
                                                        <?php if ($sort_by === 'accessionNo'): ?>
                                                            <span class="sort-indicator"><?= $sort_order === 'ASC' ? '↑' : '↓' ?></span>
                                                        <?php endif; ?>
                                                    </a>
                                                </th>
                                                <th class="sortable">
                                                    <?php
                                                    $current_params = $_GET;
                                                    $current_params['sort_by'] = 'title';
                                                    $new_order = ($sort_by === 'title' && $sort_order === 'ASC') ? 'DESC' : 'ASC';
                                                    $current_params['sort_order'] = $new_order;
                                                    $sort_url = '?' . http_build_query($current_params);
                                                    ?>
                                                    <a href="<?= $sort_url ?>" class="sort-link">
                                                        Title
                                                        <?php if ($sort_by === 'title'): ?>
                                                            <span class="sort-indicator"><?= $sort_order === 'ASC' ? '↑' : '↓' ?></span>
                                                        <?php endif; ?>
                                                    </a>
                                                </th>
                                                <th class="sortable">
                                                    <?php
                                                    $current_params = $_GET;
                                                    $current_params['sort_by'] = 'author_name';
                                                    $new_order = ($sort_by === 'author_name' && $sort_order === 'ASC') ? 'DESC' : 'ASC';
                                                    $current_params['sort_order'] = $new_order;
                                                    $sort_url = '?' . http_build_query($current_params);
                                                    ?>
                                                    <a href="<?= $sort_url ?>" class="sort-link">
                                                        Author
                                                        <?php if ($sort_by === 'author_name'): ?>
                                                            <span class="sort-indicator"><?= $sort_order === 'ASC' ? '↑' : '↓' ?></span>
                                                        <?php endif; ?>
                                                    </a>
                                                </th>
                                                <th class="sortable">
                                                    <?php
                                                    $current_params = $_GET;
                                                    $current_params['sort_by'] = 'category_name';
                                                    $new_order = ($sort_by === 'category_name' && $sort_order === 'ASC') ? 'DESC' : 'ASC';
                                                    $current_params['sort_order'] = $new_order;
                                                    $sort_url = '?' . http_build_query($current_params);
                                                    ?>
                                                    <a href="<?= $sort_url ?>" class="sort-link">
                                                        Category
                                                        <?php if ($sort_by === 'category_name'): ?>
                                                            <span class="sort-indicator"><?= $sort_order === 'ASC' ? '↑' : '↓' ?></span>
                                                        <?php endif; ?>
                                                    </a>
                                                </th>
                                                <th>Language</th>
                                                <th>Publisher</th>
                                                <th class="sortable">
                                                    <?php
                                                    $current_params = $_GET;
                                                    $current_params['sort_by'] = 'publishedYear';
                                                    $new_order = ($sort_by === 'publishedYear' && $sort_order === 'ASC') ? 'DESC' : 'ASC';
                                                    $current_params['sort_order'] = $new_order;
                                                    $sort_url = '?' . http_build_query($current_params);
                                                    ?>
                                                    <a href="<?= $sort_url ?>" class="sort-link">
                                                        Year
                                                        <?php if ($sort_by === 'publishedYear'): ?>
                                                            <span class="sort-indicator"><?= $sort_order === 'ASC' ? '↑' : '↓' ?></span>
                                                        <?php endif; ?>
                                                    </a>
                                                </th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($books as $book): ?>
                                                <tr>
                                                    <td>
                                                        <?php
                                                        $image_url = get_book_image_url($book['id'], true); // true for thumbnail
                                                        if ($image_url):
                                                        ?>
                                                            <img src="<?= $image_url ?>"
                                                                 alt="Book Cover"
                                                                 class="book-cover-thumb"
                                                                 style="width: 40px; height: 60px; object-fit: cover; border-radius: 4px; border: 1px solid #ddd;"
                                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                                            <div class="book-cover-fallback" style="display: none; width: 40px; height: 60px; background: #f5f5f5; border: 1px solid #ddd; border-radius: 4px; align-items: center; justify-content: center; font-size: 12px; color: #666;">📚</div>
                                                        <?php else: ?>
                                                            <div style="width: 40px; height: 60px; background: #f5f5f5; border: 1px solid #ddd; border-radius: 4px; display: flex; align-items: center; justify-content: center; font-size: 20px; color: #666;">📚</div>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="font-mono font-medium"><?= htmlspecialchars($book['accessionNo']) ?></span>
                                                    </td>
                                                    <td>
                                                        <div>
                                                            <div class="font-medium"><?= htmlspecialchars($book['title']) ?></div>
                                                            <?php if (!empty($book['titleNepali'])): ?>
                                                                <div class="text-sm text-muted nepali-text"><?= htmlspecialchars($book['titleNepali']) ?></div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div>
                                                            <div><?= htmlspecialchars($book['author_name']) ?></div>
                                                            <?php if (!empty($book['author_nameNepali'])): ?>
                                                                <div class="text-sm text-muted nepali-text"><?= htmlspecialchars($book['author_nameNepali']) ?></div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div>
                                                            <div><?= htmlspecialchars($book['category_name']) ?></div>
                                                            <?php if (!empty($book['category_nameNepali'])): ?>
                                                                <div class="text-sm text-muted nepali-text"><?= htmlspecialchars($book['category_nameNepali']) ?></div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div>
                                                            <div><?= htmlspecialchars($book['language_name']) ?></div>
                                                            <?php if (!empty($book['language_nameNepali'])): ?>
                                                                <div class="text-sm text-muted nepali-text"><?= htmlspecialchars($book['language_nameNepali']) ?></div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <?php if (!empty($book['publisher_name'])): ?>
                                                            <div>
                                                                <div><?= htmlspecialchars($book['publisher_name']) ?></div>
                                                                <?php if (!empty($book['publisher_nameNepali'])): ?>
                                                                    <div class="text-sm text-muted nepali-text"><?= htmlspecialchars($book['publisher_nameNepali']) ?></div>
                                                                <?php endif; ?>
                                                            </div>
                                                        <?php elseif (!empty($book['publisher'])): ?>
                                                            <div class="text-muted"><?= htmlspecialchars($book['publisher']) ?></div>
                                                        <?php else: ?>
                                                            <span class="text-muted">—</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?= htmlspecialchars($book['publishedYear']) ?></td>
                                                    <td>
                                                        <?php if ($book['isAvailable']): ?>
                                                            <span class="badge badge-success">Available</span>
                                                        <?php else: ?>
                                                            <span class="badge badge-warning">Not Available</span>
                                                        <?php endif; ?>

                                                        <?php if (!empty($book['condition_name'])): ?>
                                                            <div class="text-sm">
                                                                <span class="badge" style="background-color: <?= htmlspecialchars($book['condition_color']) ?>; color: white;">
                                                                    <?= htmlspecialchars($book['condition_name']) ?>
                                                                </span>
                                                            </div>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <a href="?action=edit&id=<?= htmlspecialchars($book['id']) ?>"
                                                               class="btn btn-sm btn-secondary" title="Edit Book">
                                                                ✏️
                                                            </a>
                                                            <button type="button"
                                                                    class="btn btn-sm btn-danger delete-btn"
                                                                    data-id="<?= htmlspecialchars($book['id']) ?>"
                                                                    data-title="<?= htmlspecialchars($book['title']) ?>"
                                                                    title="Delete Book">
                                                                🗑️
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <?php if ($total_books > $per_page): ?>
                                    <div class="pagination-wrapper">
                                        <?php
                                        $total_pages = ceil($total_books / $per_page);
                                        $query_params = $_GET;
                                        unset($query_params['page']);
                                        $base_url = '?' . http_build_query($query_params);
                                        if (!empty($query_params)) {
                                            $base_url .= '&';
                                        }
                                        ?>

                                        <div class="pagination">
                                            <?php if ($page > 1): ?>
                                                <a href="<?= $base_url ?>page=<?= $page - 1 ?>" class="pagination-btn">‹ Previous</a>
                                            <?php endif; ?>

                                            <?php
                                            $start_page = max(1, $page - 2);
                                            $end_page = min($total_pages, $page + 2);

                                            for ($i = $start_page; $i <= $end_page; $i++):
                                            ?>
                                                <a href="<?= $base_url ?>page=<?= $i ?>"
                                                   class="pagination-btn <?= $i === $page ? 'active' : '' ?>">
                                                    <?= $i ?>
                                                </a>
                                            <?php endfor; ?>

                                            <?php if ($page < $total_pages): ?>
                                                <a href="<?= $base_url ?>page=<?= $page + 1 ?>" class="pagination-btn">Next ›</a>
                                            <?php endif; ?>
                                        </div>

                                        <div class="pagination-info">
                                            Showing <?= number_format(($page - 1) * $per_page + 1) ?> to
                                            <?= number_format(min($page * $per_page, $total_books)) ?> of
                                            <?= number_format($total_books) ?> books
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                <?php elseif ($action === 'add' || $action === 'edit'): ?>
                    <!-- Book Form -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <h2 class="page-title"><?= $action === 'add' ? 'Add New Book' : 'Edit Book' ?></h2>
                            <p class="page-subtitle"><?= $action === 'add' ? 'Add a new book to the library collection' : 'Update book information' ?></p>
                        </div>
                        <div class="page-header-actions">
                            <a href="books.php" class="btn btn-secondary">
                                <span style="margin-right: 0.5rem;">←</span>
                                Back to Books
                            </a>
                        </div>
                    </div>

                    <form method="POST" enctype="multipart/form-data" class="book-form">
                        <input type="hidden" name="action" value="<?= $action ?>">
                        <?php if ($action === 'edit' && $book_data): ?>
                            <input type="hidden" name="id" value="<?= htmlspecialchars($book_data['id']) ?>">
                        <?php endif; ?>

                        <div class="form-grid">
                            <!-- Basic Information -->
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Basic Information</h3>
                                </div>
                                <div class="card-body">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="accessionNo" class="form-label required">Accession Number</label>
                                            <input type="text"
                                                   id="accessionNo"
                                                   name="accessionNo"
                                                   class="input"
                                                   value="<?= htmlspecialchars($book_data['accessionNo'] ?? 'MVKL') ?>"
                                                   placeholder="MVKL1, MVKL001, MVKL1-01"
                                                   required>
                                            <div class="form-help">Must start with MVKL followed by numbers (e.g., MVKL1, MVKL001, MVKL1-01)</div>
                                            <div id="accession-validation" class="form-validation"></div>
                                        </div>

                                        <div class="form-group">
                                            <label for="isbn" class="form-label">ISBN</label>
                                            <input type="text"
                                                   id="isbn"
                                                   name="isbn"
                                                   class="input"
                                                   value="<?= htmlspecialchars($book_data['isbn'] ?? '') ?>"
                                                   placeholder="978-0-123456-78-9">
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="title" class="form-label required">Title</label>
                                            <input type="text"
                                                   id="title"
                                                   name="title"
                                                   class="input"
                                                   value="<?= htmlspecialchars($book_data['title'] ?? '') ?>"
                                                   placeholder="Book title"
                                                   required>
                                        </div>

                                        <div class="form-group">
                                            <label for="titleNepali" class="form-label">Title (Nepali/Devanagari)</label>
                                            <input type="text"
                                                   id="titleNepali"
                                                   name="titleNepali"
                                                   class="input nepali-input"
                                                   value="<?= htmlspecialchars($book_data['titleNepali'] ?? '') ?>"
                                                   placeholder="पुस्तकको नाम">
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="authorId" class="form-label required">Author</label>
                                            <select id="authorId" name="authorId" class="input" required>
                                                <option value="">Select Author</option>
                                                <?php foreach ($authors as $author): ?>
                                                    <option value="<?= htmlspecialchars($author['id']) ?>"
                                                            <?= ($book_data['authorId'] ?? '') === $author['id'] ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($author['name']) ?>
                                                        <?php if (!empty($author['nameNepali'])): ?>
                                                            (<?= htmlspecialchars($author['nameNepali']) ?>)
                                                        <?php endif; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-help">
                                                <a href="authors.php?action=add" target="_blank">Add new author</a>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="publishedYear" class="form-label required">Published Year</label>
                                            <input type="number"
                                                   id="publishedYear"
                                                   name="publishedYear"
                                                   class="input"
                                                   value="<?= htmlspecialchars($book_data['publishedYear'] ?? date('Y')) ?>"
                                                   min="1000"
                                                   max="<?= date('Y') + 1 ?>"
                                                   required>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="categoryId" class="form-label required">Category</label>
                                            <select id="categoryId" name="categoryId" class="input" required>
                                                <option value="">Select Category</option>
                                                <?php foreach ($categories as $category): ?>
                                                    <option value="<?= htmlspecialchars($category['id']) ?>"
                                                            <?= ($book_data['categoryId'] ?? '') === $category['id'] ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($category['name']) ?>
                                                        <?php if (!empty($category['nameNepali'])): ?>
                                                            (<?= htmlspecialchars($category['nameNepali']) ?>)
                                                        <?php endif; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-help">
                                                <a href="categories.php?action=add" target="_blank">Add new category</a>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="languageId" class="form-label required">Language</label>
                                            <select id="languageId" name="languageId" class="input" required>
                                                <option value="">Select Language</option>
                                                <?php foreach ($languages as $language): ?>
                                                    <option value="<?= htmlspecialchars($language['id']) ?>"
                                                            <?= ($book_data['languageId'] ?? '') === $language['id'] ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($language['name']) ?>
                                                        <?php if (!empty($language['nameNepali'])): ?>
                                                            (<?= htmlspecialchars($language['nameNepali']) ?>)
                                                        <?php endif; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-help">
                                                <a href="languages.php?action=add" target="_blank">Add new language</a>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="book_image" class="form-label">Book Cover Image</label>
                                            <input type="file"
                                                   id="book_image"
                                                   name="book_image"
                                                   class="input"
                                                   accept="image/jpeg,image/jpg,image/png,image/webp">
                                            <div class="form-help">
                                                Upload book cover image. Supported formats: JPG, PNG, WebP. Max size: <?= MAX_FILE_SIZE / 1024 / 1024 ?>MB.
                                                Image will be automatically resized and compressed to ~30KB JPEG format.
                                            </div>
                                            <?php if ($image_upload_error): ?>
                                                <div class="form-error"><?= htmlspecialchars($image_upload_error) ?></div>
                                            <?php endif; ?>
                                        </div>

                                        <?php
                                        if ($action === 'edit' && $book_data) {
                                            $current_image_url = get_book_image_url($book_data['id'], false); // false for full size
                                            if ($current_image_url):
                                        ?>
                                            <div class="form-group">
                                                <label class="form-label">Current Image</label>
                                                <div class="current-image-preview">
                                                    <img src="<?= $current_image_url ?>"
                                                         alt="Book Cover"
                                                         class="book-cover-preview"
                                                         style="max-width: 150px; max-height: 200px; border: 1px solid #ddd; border-radius: 4px;">
                                                    <div class="form-help">Current book cover image</div>
                                                </div>
                                            </div>
                                        <?php
                                            endif;
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Publisher Information -->
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Publisher Information</h3>
                                </div>
                                <div class="card-body">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="publisherId" class="form-label">Publisher</label>
                                            <select id="publisherId" name="publisherId" class="input">
                                                <option value="">Select Publisher</option>
                                                <?php foreach ($publishers as $publisher): ?>
                                                    <option value="<?= htmlspecialchars($publisher['id']) ?>"
                                                            <?= ($book_data['publisherId'] ?? '') === $publisher['id'] ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($publisher['name']) ?>
                                                        <?php if (!empty($publisher['nameNepali'])): ?>
                                                            (<?= htmlspecialchars($publisher['nameNepali']) ?>)
                                                        <?php endif; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-help">
                                                <a href="publishers.php?action=add" target="_blank">Add new publisher</a>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="publisher" class="form-label">Publisher (Text)</label>
                                            <input type="text"
                                                   id="publisher"
                                                   name="publisher"
                                                   class="input"
                                                   value="<?= htmlspecialchars($book_data['publisher'] ?? '') ?>"
                                                   placeholder="Publisher name (if not in list)">
                                            <div class="form-help">Use this field if publisher is not in the dropdown list</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Additional Details -->
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Additional Details</h3>
                                </div>
                                <div class="card-body">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="subjectId" class="form-label">Subject</label>
                                            <select id="subjectId" name="subjectId" class="input">
                                                <option value="">Select Subject</option>
                                                <?php foreach ($subjects as $subject): ?>
                                                    <option value="<?= htmlspecialchars($subject['id']) ?>"
                                                            data-category="<?= htmlspecialchars($subject['categoryId']) ?>"
                                                            <?= ($book_data['subjectId'] ?? '') === $subject['id'] ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($subject['name']) ?>
                                                        <?php if (!empty($subject['nameNepali'])): ?>
                                                            (<?= htmlspecialchars($subject['nameNepali']) ?>)
                                                        <?php endif; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="seriesId" class="form-label">Book Series</label>
                                            <select id="seriesId" name="seriesId" class="input">
                                                <option value="">Select Series</option>
                                                <?php foreach ($series as $serie): ?>
                                                    <option value="<?= htmlspecialchars($serie['id']) ?>"
                                                            <?= ($book_data['seriesId'] ?? '') === $serie['id'] ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($serie['name']) ?>
                                                        <?php if (!empty($serie['nameNepali'])): ?>
                                                            (<?= htmlspecialchars($serie['nameNepali']) ?>)
                                                        <?php endif; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="pages" class="form-label">Pages</label>
                                            <input type="number"
                                                   id="pages"
                                                   name="pages"
                                                   class="input"
                                                   value="<?= htmlspecialchars($book_data['pages'] ?? '') ?>"
                                                   min="1"
                                                   placeholder="Number of pages">
                                        </div>

                                        <div class="form-group">
                                            <label for="edition" class="form-label">Edition</label>
                                            <input type="text"
                                                   id="edition"
                                                   name="edition"
                                                   class="input"
                                                   value="<?= htmlspecialchars($book_data['edition'] ?? '') ?>"
                                                   placeholder="1st Edition, Revised Edition, etc.">
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="price" class="form-label">Price (NPR)</label>
                                            <input type="number"
                                                   id="price"
                                                   name="price"
                                                   class="input"
                                                   value="<?= htmlspecialchars($book_data['price'] ?? '') ?>"
                                                   step="0.01"
                                                   min="0"
                                                   placeholder="0.00">
                                        </div>

                                        <div class="form-group">
                                            <label for="originalPrice" class="form-label">Original Price</label>
                                            <div class="input-group">
                                                <input type="number"
                                                       id="originalPrice"
                                                       name="originalPrice"
                                                       class="input"
                                                       value="<?= htmlspecialchars($book_data['originalPrice'] ?? '') ?>"
                                                       step="0.01"
                                                       min="0"
                                                       placeholder="0.00">
                                                <select name="originalCurrency" class="input" style="max-width: 100px;">
                                                    <option value="NPR" <?= ($book_data['originalCurrency'] ?? 'NPR') === 'NPR' ? 'selected' : '' ?>>NPR</option>
                                                    <option value="USD" <?= ($book_data['originalCurrency'] ?? '') === 'USD' ? 'selected' : '' ?>>USD</option>
                                                    <option value="INR" <?= ($book_data['originalCurrency'] ?? '') === 'INR' ? 'selected' : '' ?>>INR</option>
                                                    <option value="EUR" <?= ($book_data['originalCurrency'] ?? '') === 'EUR' ? 'selected' : '' ?>>EUR</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Location & Physical Details -->
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Location & Physical Details</h3>
                                </div>
                                <div class="card-body">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="locationId" class="form-label">Location</label>
                                            <select id="locationId" name="locationId" class="input">
                                                <option value="">Select Location</option>
                                                <?php foreach ($locations as $location): ?>
                                                    <option value="<?= htmlspecialchars($location['id']) ?>"
                                                            <?= ($book_data['locationId'] ?? '') === $location['id'] ? 'selected' : '' ?>>
                                                        Shelf <?= htmlspecialchars($location['shelf']) ?>
                                                        <?php if (!empty($location['row'])): ?>
                                                            - Row <?= htmlspecialchars($location['row']) ?>
                                                        <?php endif; ?>
                                                        <?php if (!empty($location['description'])): ?>
                                                            (<?= htmlspecialchars($location['description']) ?>)
                                                        <?php endif; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="conditionId" class="form-label">Condition</label>
                                            <select id="conditionId" name="conditionId" class="input">
                                                <option value="">Select Condition</option>
                                                <?php foreach ($conditions as $condition): ?>
                                                    <option value="<?= htmlspecialchars($condition['id']) ?>"
                                                            <?= ($book_data['conditionId'] ?? '') === $condition['id'] ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($condition['name']) ?>
                                                        <?php if (!empty($condition['description'])): ?>
                                                            - <?= htmlspecialchars($condition['description']) ?>
                                                        <?php endif; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="shelf" class="form-label">Shelf (Manual)</label>
                                            <input type="text"
                                                   id="shelf"
                                                   name="shelf"
                                                   class="input"
                                                   value="<?= htmlspecialchars($book_data['shelf'] ?? '') ?>"
                                                   placeholder="A, B, C, etc.">
                                        </div>

                                        <div class="form-group">
                                            <label for="row" class="form-label">Row (Manual)</label>
                                            <input type="text"
                                                   id="row"
                                                   name="row"
                                                   class="input"
                                                   value="<?= htmlspecialchars($book_data['row'] ?? '') ?>"
                                                   placeholder="1, 2, 3, etc.">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Source & Acquisition -->
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Source & Acquisition</h3>
                                </div>
                                <div class="card-body">
                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="sourceId" class="form-label">Source</label>
                                            <select id="sourceId" name="sourceId" class="input">
                                                <option value="">Select Source</option>
                                                <?php foreach ($sources as $source): ?>
                                                    <option value="<?= htmlspecialchars($source['id']) ?>"
                                                            <?= ($book_data['sourceId'] ?? '') === $source['id'] ? 'selected' : '' ?>>
                                                        <?= htmlspecialchars($source['name']) ?>
                                                        <?php if (!empty($source['nameNepali'])): ?>
                                                            (<?= htmlspecialchars($source['nameNepali']) ?>)
                                                        <?php endif; ?>
                                                        - <?= htmlspecialchars($source['type']) ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="dateReceived" class="form-label">Date Received</label>
                                            <input type="date"
                                                   id="dateReceived"
                                                   name="dateReceived"
                                                   class="input"
                                                   value="<?= $book_data['dateReceived'] ? date('Y-m-d', strtotime($book_data['dateReceived'])) : '' ?>">
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group">
                                            <label for="bookNo" class="form-label">Book Number</label>
                                            <input type="text"
                                                   id="bookNo"
                                                   name="bookNo"
                                                   class="input"
                                                   value="<?= htmlspecialchars($book_data['bookNo'] ?? '') ?>"
                                                   placeholder="Internal book number">
                                        </div>

                                        <div class="form-group">
                                            <label for="billNoAndDate" class="form-label">Bill No. & Date</label>
                                            <input type="text"
                                                   id="billNoAndDate"
                                                   name="billNoAndDate"
                                                   class="input"
                                                   value="<?= htmlspecialchars($book_data['billNoAndDate'] ?? '') ?>"
                                                   placeholder="Bill number and date">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="remark" class="form-label">Remarks</label>
                                        <textarea id="remark"
                                                  name="remark"
                                                  class="input"
                                                  rows="3"
                                                  placeholder="Additional notes or remarks"><?= htmlspecialchars($book_data['remark'] ?? '') ?></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Status -->
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Status</h3>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label class="checkbox-label">
                                            <input type="checkbox"
                                                   name="isAvailable"
                                                   value="1"
                                                   <?= ($book_data['isAvailable'] ?? 1) ? 'checked' : '' ?>>
                                            <span class="checkbox-text">Book is available for borrowing</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <span style="margin-right: 0.5rem;">💾</span>
                                <?= $action === 'add' ? 'Add Book' : 'Update Book' ?>
                            </button>
                            <a href="books.php" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>

                <?php endif; ?>

            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <script>
        // Mobile Menu Functionality
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            sidebar.classList.toggle('open');
            overlay.classList.toggle('active');
            if (sidebar.classList.contains('open')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        }

        function closeMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            sidebar.classList.remove('open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        // Initialize mobile menu
        document.addEventListener('DOMContentLoaded', () => {
            const mobileToggle = document.getElementById('mobile-menu-toggle');
            if (mobileToggle) {
                mobileToggle.addEventListener('click', toggleMobileMenu);
            }

            const mobileOverlay = document.getElementById('mobile-overlay');
            if (mobileOverlay) {
                mobileOverlay.addEventListener('click', closeMobileMenu);
            }

            const mobileCloseBtn = document.getElementById('mobile-close-btn');
            if (mobileCloseBtn) {
                mobileCloseBtn.addEventListener('click', closeMobileMenu);
            }

            // Close mobile menu when clicking on nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth <= 1023) {
                        closeMobileMenu();
                    }
                });
            });
        });

        // Close mobile menu on window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 1023) {
                closeMobileMenu();
            }
        });

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeMobileMenu();
            }
        });

        <?php if ($action === 'list'): ?>
        // Delete functionality
        document.addEventListener('DOMContentLoaded', () => {
            document.querySelectorAll('.delete-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const bookId = this.dataset.id;
                    const bookTitle = this.dataset.title;

                    if (confirm(`Are you sure you want to delete "${bookTitle}"?\n\nThis action cannot be undone.`)) {
                        fetch(`?ajax_delete=1&id=${bookId}`)
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    location.reload();
                                } else {
                                    alert('Error: ' + data.message);
                                }
                            })
                            .catch(error => {
                                alert('Error deleting book: ' + error.message);
                            });
                    }
                });
            });
        });
        <?php endif; ?>

        <?php if ($action === 'add' || $action === 'edit'): ?>
        // Form functionality
        document.addEventListener('DOMContentLoaded', () => {
            const accessionInput = document.getElementById('accessionNo');
            const validationDiv = document.getElementById('accession-validation');
            const categorySelect = document.getElementById('categoryId');
            const subjectSelect = document.getElementById('subjectId');

            // Accession number validation
            if (accessionInput) {
                let validationTimeout;

                accessionInput.addEventListener('input', function() {
                    clearTimeout(validationTimeout);
                    const accessionNo = this.value.trim();

                    if (accessionNo.length < 5) {
                        validationDiv.innerHTML = '';
                        return;
                    }

                    validationTimeout = setTimeout(() => {
                        checkAccessionNumber(accessionNo);
                    }, 500);
                });
            }

            function checkAccessionNumber(accessionNo) {
                const excludeId = '<?= $book_data['id'] ?? '' ?>';
                const url = `?check_accession=1&accession_no=${encodeURIComponent(accessionNo)}&exclude_id=${excludeId}`;

                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        if (data.available) {
                            validationDiv.innerHTML = '<div class="validation-success">✅ ' + data.message + '</div>';
                        } else {
                            validationDiv.innerHTML = '<div class="validation-error">❌ ' + data.message +
                                (data.existing_book ? ' (Book: ' + data.existing_book + ')' : '') + '</div>';
                        }
                    })
                    .catch(error => {
                        validationDiv.innerHTML = '<div class="validation-error">❌ Error checking accession number</div>';
                    });
            }

            // Filter subjects by category
            if (categorySelect && subjectSelect) {
                const allSubjects = Array.from(subjectSelect.options).slice(1); // Skip first "Select Subject" option

                categorySelect.addEventListener('change', function() {
                    const selectedCategory = this.value;

                    // Clear current options except the first one
                    subjectSelect.innerHTML = '<option value="">Select Subject</option>';

                    if (selectedCategory) {
                        // Add subjects that belong to the selected category
                        allSubjects.forEach(option => {
                            if (option.dataset.category === selectedCategory) {
                                subjectSelect.appendChild(option.cloneNode(true));
                            }
                        });
                    } else {
                        // Add all subjects if no category is selected
                        allSubjects.forEach(option => {
                            subjectSelect.appendChild(option.cloneNode(true));
                        });
                    }
                });

                // Trigger initial filtering
                categorySelect.dispatchEvent(new Event('change'));
            }

            // Form validation
            const form = document.querySelector('.book-form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const accessionNo = accessionInput.value.trim();

                    // Check accession number format
                    if (!accessionNo.match(/^MVKL\d+(-\d+)?$/)) {
                        e.preventDefault();
                        alert('Accession number must start with MVKL followed by numbers (e.g., MVKL1, MVKL001, MVKL1-01)');
                        accessionInput.focus();
                        return;
                    }

                    // Check if validation shows error
                    const validationError = validationDiv.querySelector('.validation-error');
                    if (validationError) {
                        e.preventDefault();
                        alert('Please resolve the accession number conflict before submitting.');
                        accessionInput.focus();
                        return;
                    }
                });
            }
        });
        <?php endif; ?>

        console.log('Books management page loaded');
    </script>
</body>
</html>
