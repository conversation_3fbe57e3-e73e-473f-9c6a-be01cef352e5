<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Subjects Management
 */

require_once '../config/config.php';
require_once '../includes/Database.php';
require_once '../includes/functions.php';

// Check if user is logged in
require_admin();

// Initialize database
$db = new Database();

// Handle actions
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? null;
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
            case 'edit':
                $name = trim($_POST['name'] ?? '');
                $nameNepali = trim($_POST['nameNepali'] ?? '') ?: null;
                $code = trim($_POST['code'] ?? '') ?: null;
                $categoryId = trim($_POST['categoryId'] ?? '');
                $description = trim($_POST['description'] ?? '') ?: null;
                $isActive = isset($_POST['isActive']) ? 1 : 0;
                
                // Validation
                if (empty($name)) {
                    $error = 'Subject name is required';
                } elseif (empty($categoryId)) {
                    $error = 'Category is required';
                } else {
                    try {
                        if ($_POST['action'] === 'add') {
                            // Check if subject already exists
                            $existing = $db->fetch("SELECT id FROM subjects WHERE name = ? AND isDeleted = 0", [$name]);
                            if ($existing) {
                                $error = 'Subject with this name already exists';
                            } elseif (!empty($code)) {
                                $existing_code = $db->fetch("SELECT id FROM subjects WHERE code = ? AND isDeleted = 0", [$code]);
                                if ($existing_code) {
                                    $error = 'Subject with this code already exists';
                                }
                            }
                            
                            if (!$error) {
                                $subject_id = 'subj' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
                                $data = [
                                    'id' => $subject_id,
                                    'name' => $name,
                                    'nameNepali' => $nameNepali,
                                    'code' => $code,
                                    'categoryId' => $categoryId,
                                    'description' => $description,
                                    'isActive' => $isActive,
                                    'createdAt' => date('Y-m-d H:i:s'),
                                    'updatedAt' => date('Y-m-d H:i:s')
                                ];
                                $db->insert('subjects', $data);
                                $message = 'Subject added successfully';
                                log_activity("Added subject: $name", 'INFO');
                                $action = 'list';
                            }
                        } else {
                            // Edit existing subject
                            $edit_id = $_POST['id'] ?? '';
                            if ($edit_id) {
                                // Check if name conflicts with other subjects
                                $existing = $db->fetch("SELECT id FROM subjects WHERE name = ? AND id != ? AND isDeleted = 0", [$name, $edit_id]);
                                if ($existing) {
                                    $error = 'Subject with this name already exists';
                                } elseif (!empty($code)) {
                                    $existing_code = $db->fetch("SELECT id FROM subjects WHERE code = ? AND id != ? AND isDeleted = 0", [$code, $edit_id]);
                                    if ($existing_code) {
                                        $error = 'Subject with this code already exists';
                                    }
                                }
                                
                                if (!$error) {
                                    $data = [
                                        'name' => $name,
                                        'nameNepali' => $nameNepali,
                                        'code' => $code,
                                        'categoryId' => $categoryId,
                                        'description' => $description,
                                        'isActive' => $isActive,
                                        'updatedAt' => date('Y-m-d H:i:s')
                                    ];
                                    $db->update('subjects', $data, 'id = ?', [$edit_id]);
                                    $message = 'Subject updated successfully';
                                    log_activity("Updated subject: $name", 'INFO');
                                    $action = 'list';
                                }
                            }
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                        log_activity("Subject save error: " . $e->getMessage(), 'ERROR');
                    }
                }
                break;
                
            case 'delete':
                $delete_id = $_POST['id'] ?? '';
                if ($delete_id) {
                    try {
                        // Check if subject has books
                        $book_count = $db->fetchColumn("SELECT COUNT(*) FROM books WHERE subjectId = ? AND isDeleted = 0", [$delete_id]);
                        if ($book_count > 0) {
                            $error = "Cannot delete subject. $book_count books are associated with this subject.";
                        } else {
                            $subject_name = $db->fetchColumn("SELECT name FROM subjects WHERE id = ?", [$delete_id]);
                            $db->delete('subjects', 'id = ?', [$delete_id]);
                            $message = 'Subject deleted successfully';
                            log_activity("Deleted subject: $subject_name", 'INFO');
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                        log_activity("Subject delete error: " . $e->getMessage(), 'ERROR');
                    }
                }
                break;
        }
    }
}

// Handle AJAX delete request
if (isset($_GET['ajax_delete']) && $_GET['ajax_delete'] === '1' && $id) {
    header('Content-Type: application/json');
    try {
        // Check if subject has books
        $book_count = $db->fetchColumn("SELECT COUNT(*) FROM books WHERE subjectId = ? AND isDeleted = 0", [$id]);
        if ($book_count > 0) {
            echo json_encode(['success' => false, 'message' => "Cannot delete subject. $book_count books are associated with this subject."]);
        } else {
            $subject_name = $db->fetchColumn("SELECT name FROM subjects WHERE id = ?", [$id]);
            $db->delete('subjects', 'id = ?', [$id]);
            log_activity("Deleted subject: $subject_name", 'INFO');
            echo json_encode(['success' => true, 'message' => 'Subject deleted successfully']);
        }
    } catch (Exception $e) {
        log_activity("Subject delete error: " . $e->getMessage(), 'ERROR');
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
    exit;
}

// Get data for edit form
$subject_data = null;
if ($action === 'edit' && $id) {
    $subject_data = $db->fetch("SELECT * FROM subjects WHERE id = ? AND isDeleted = 0", [$id]);
    if (!$subject_data) {
        $error = 'Subject not found';
        $action = 'list';
    }
}

// Get categories for dropdown
$categories = [];
try {
    $categories = $db->fetchAll("SELECT id, name, nameNepali FROM categories WHERE isDeleted = 0 ORDER BY name ASC");
} catch (Exception $e) {
    log_activity("Categories fetch error: " . $e->getMessage(), 'ERROR');
}

// Get subjects list for list view
$subjects = [];
$total_subjects = 0;
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = ADMIN_ITEMS_PER_PAGE;
$search = trim($_GET['search'] ?? '');
$category_filter = $_GET['category'] ?? '';
$status_filter = $_GET['status'] ?? '';

if ($action === 'list') {
    try {
        $where_conditions = ['s.isDeleted = 0'];
        $params = [];
        
        if (!empty($search)) {
            $where_conditions[] = '(s.name LIKE ? OR s.nameNepali LIKE ? OR s.code LIKE ? OR s.description LIKE ?)';
            $search_param = "%$search%";
            $params[] = $search_param;
            $params[] = $search_param;
            $params[] = $search_param;
            $params[] = $search_param;
        }
        
        if (!empty($category_filter)) {
            $where_conditions[] = 's.categoryId = ?';
            $params[] = $category_filter;
        }
        
        if ($status_filter === 'active') {
            $where_conditions[] = 's.isActive = 1';
        } elseif ($status_filter === 'inactive') {
            $where_conditions[] = 's.isActive = 0';
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        // Get total count
        $total_subjects = $db->fetchColumn("
            SELECT COUNT(*) 
            FROM subjects s 
            LEFT JOIN categories c ON s.categoryId = c.id 
            WHERE $where_clause
        ", $params);
        
        // Get subjects with category and book count
        $offset = ($page - 1) * $per_page;
        $subjects = $db->fetchAll("
            SELECT s.*, 
                   c.name as categoryName,
                   c.nameNepali as categoryNameNepali,
                   COUNT(b.id) as bookCount
            FROM subjects s
            LEFT JOIN categories c ON s.categoryId = c.id
            LEFT JOIN books b ON s.id = b.subjectId AND b.isDeleted = 0
            WHERE $where_clause
            GROUP BY s.id
            ORDER BY s.name ASC
            LIMIT $per_page OFFSET $offset
        ", $params);
        
    } catch (Exception $e) {
        $error = 'Database error: ' . $e->getMessage();
        log_activity("Subjects list error: " . $e->getMessage(), 'ERROR');
    }
}

$page_title = 'Subjects - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="<?= asset_url('css/style.css') ?>">
    <link rel="stylesheet" href="<?= asset_url('css/admin.css') ?>">
    
    <!-- Favicon and App Icons -->
    <?= generate_favicon_tags() ?>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <button class="mobile-close-btn" id="mobile-close-btn">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <div class="sidebar-logo">
                    <img src="<?= icon_url(48) ?>" alt="<?= APP_NAME ?> Icon" class="sidebar-logo-image">
                </div>
                <h2 class="sidebar-title">Admin Panel</h2>
                <p class="sidebar-subtitle">मैथिली विकास कोष</p>
            </div>

            <!-- Mobile User Section -->
            <div class="mobile-user-section">
                <div class="mobile-user-info">
                    <div class="mobile-user-avatar">
                        <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                    </div>
                    <div class="mobile-user-details">
                        <span class="mobile-user-name"><?= htmlspecialchars($_SESSION['admin_name']) ?></span>
                        <span class="mobile-user-role">Administrator</span>
                    </div>
                </div>
                <a href="logout.php" class="mobile-logout-btn">
                    <span class="logout-icon">🚪</span>
                    Logout
                </a>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <div class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Dashboard
                        </a>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Book Management</div>
                    <div class="nav-item">
                        <a href="books.php" class="nav-link">
                            <span class="nav-icon">📚</span>
                            Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="books.php?action=add" class="nav-link">
                            <span class="nav-icon">➕</span>
                            Add Book
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-books.php" class="nav-link">
                            <span class="nav-icon">📥</span>
                            Import Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-images.php" class="nav-link">
                            <span class="nav-icon">🖼️</span>
                            Import Images
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Catalog Management</div>
                    <div class="nav-item">
                        <a href="authors.php" class="nav-link">
                            <span class="nav-icon">✍️</span>
                            Authors
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="categories.php" class="nav-link">
                            <span class="nav-icon">🏷️</span>
                            Categories
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="publishers.php" class="nav-link">
                            <span class="nav-icon">🏢</span>
                            Publishers
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="languages.php" class="nav-link">
                            <span class="nav-icon">🌐</span>
                            Languages
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="subjects.php" class="nav-link active">
                            <span class="nav-icon">📖</span>
                            Subjects
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="series.php" class="nav-link">
                            <span class="nav-icon">📑</span>
                            Book Series
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="locations.php" class="nav-link">
                            <span class="nav-icon">📍</span>
                            Locations
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="conditions.php" class="nav-link">
                            <span class="nav-icon">🔧</span>
                            Conditions
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="sources.php" class="nav-link">
                            <span class="nav-icon">🎁</span>
                            Sources
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Reports & Analytics</div>
                    <div class="nav-item">
                        <a href="reports.php" class="nav-link">
                            <span class="nav-icon">📈</span>
                            Collection Reports
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="analytics.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Analytics
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <div class="nav-item">
                        <a href="users.php" class="nav-link">
                            <span class="nav-icon">👤</span>
                            Admin Users
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="settings.php" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            Settings
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="backup.php" class="nav-link">
                            <span class="nav-icon">💾</span>
                            Backup & Export
                        </a>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <header class="admin-header">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="header-title">
                            <span class="title-icon">📖</span>
                            Subjects Management
                        </h1>
                        <p class="header-subtitle">Manage book subjects and their categories</p>
                    </div>
                    <div class="header-actions">
                        <div class="user-info">
                            <span class="welcome-text">Welcome, <?= htmlspecialchars($_SESSION['admin_name']) ?>!</span>
                            <div class="user-menu">
                                <div class="user-avatar">
                                    <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                                </div>
                            </div>
                        </div>
                        <a href="logout.php" class="btn btn-secondary btn-sm">
                            <span style="margin-right: 0.5rem;">🚪</span>
                            Logout
                        </a>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Breadcrumb -->
                <nav class="breadcrumb">
                    <a href="dashboard.php" class="breadcrumb-item">Dashboard</a>
                    <span class="breadcrumb-separator">›</span>
                    <span class="breadcrumb-item active">Subjects</span>
                </nav>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <span class="alert-icon">✅</span>
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <span class="alert-icon">❌</span>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <?php if ($action === 'list'): ?>
                    <!-- Subjects List -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <h2 class="page-title">Subjects</h2>
                            <p class="page-subtitle">Manage book subjects and their categories</p>
                        </div>
                        <div class="page-header-actions">
                            <a href="?action=add" class="btn btn-primary">
                                <span style="margin-right: 0.5rem;">➕</span>
                                Add Subject
                            </a>
                        </div>
                    </div>

                    <!-- Search and Filters -->
                    <div class="card">
                        <div class="card-body">
                            <form method="GET" class="search-form">
                                <div class="search-form-row">
                                    <div class="search-input-group">
                                        <input type="text" name="search" value="<?= htmlspecialchars($search) ?>"
                                               placeholder="Search subjects by name, code, or description..." class="input">
                                        <select name="category" class="input">
                                            <option value="">All Categories</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?= htmlspecialchars($category['id']) ?>"
                                                        <?= $category_filter === $category['id'] ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($category['name']) ?>
                                                    <?php if (!empty($category['nameNepali'])): ?>
                                                        (<?= htmlspecialchars($category['nameNepali']) ?>)
                                                    <?php endif; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <select name="status" class="input">
                                            <option value="">All Status</option>
                                            <option value="active" <?= $status_filter === 'active' ? 'selected' : '' ?>>Active</option>
                                            <option value="inactive" <?= $status_filter === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                        </select>
                                        <button type="submit" class="btn btn-secondary">
                                            <span style="margin-right: 0.5rem;">🔍</span>
                                            Search
                                        </button>
                                    </div>
                                    <?php if (!empty($search) || !empty($category_filter) || !empty($status_filter)): ?>
                                        <a href="?" class="btn btn-outline">Clear</a>
                                    <?php endif; ?>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Subjects Table -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                Subjects List
                                <?php if (!empty($search)): ?>
                                    <span class="text-muted">(Search: "<?= htmlspecialchars($search) ?>")</span>
                                <?php endif; ?>
                            </h3>
                            <div class="card-header-actions">
                                <span class="text-muted">Total: <?= number_format($total_subjects) ?> subjects</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($subjects)): ?>
                                <div class="empty-state">
                                    <div class="empty-state-icon">📖</div>
                                    <h3 class="empty-state-title">No Subjects Found</h3>
                                    <p class="empty-state-description">
                                        <?php if (!empty($search) || !empty($category_filter) || !empty($status_filter)): ?>
                                            No subjects match your search criteria. Try adjusting your search terms or filters.
                                        <?php else: ?>
                                            Start by adding your first subject to organize books in the library system.
                                        <?php endif; ?>
                                    </p>
                                    <div class="empty-state-actions">
                                        <?php if (!empty($search) || !empty($category_filter) || !empty($status_filter)): ?>
                                            <a href="?" class="btn btn-secondary">Clear Filters</a>
                                        <?php endif; ?>
                                        <a href="?action=add" class="btn btn-primary">Add First Subject</a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Category</th>
                                                <th>Code</th>
                                                <th>Books</th>
                                                <th>Status</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($subjects as $subject): ?>
                                                <tr>
                                                    <td>
                                                        <div class="subject-name">
                                                            <strong><?= htmlspecialchars($subject['name']) ?></strong>
                                                            <?php if (!empty($subject['nameNepali'])): ?>
                                                                <div class="subject-name-nepali">
                                                                    <span class="nepali-text"><?= htmlspecialchars($subject['nameNepali']) ?></span>
                                                                </div>
                                                            <?php endif; ?>
                                                            <?php if (!empty($subject['description'])): ?>
                                                                <div class="subject-description">
                                                                    <?= htmlspecialchars(substr($subject['description'], 0, 80)) ?>
                                                                    <?php if (strlen($subject['description']) > 80): ?>...<?php endif; ?>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="category-info">
                                                            <strong><?= htmlspecialchars($subject['categoryName']) ?></strong>
                                                            <?php if (!empty($subject['categoryNameNepali'])): ?>
                                                                <div class="category-name-nepali">
                                                                    <span class="nepali-text"><?= htmlspecialchars($subject['categoryNameNepali']) ?></span>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <?php if (!empty($subject['code'])): ?>
                                                            <code class="subject-code"><?= htmlspecialchars($subject['code']) ?></code>
                                                        <?php else: ?>
                                                            <span class="text-muted">—</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-info"><?= number_format($subject['bookCount']) ?> books</span>
                                                    </td>
                                                    <td>
                                                        <?php if ($subject['isActive']): ?>
                                                            <span class="badge badge-success">Active</span>
                                                        <?php else: ?>
                                                            <span class="badge badge-warning">Inactive</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="text-muted"><?= date('M j, Y', strtotime($subject['createdAt'])) ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <a href="?action=edit&id=<?= urlencode($subject['id']) ?>"
                                                               class="btn btn-sm btn-outline" title="Edit Subject">
                                                                ✏️
                                                            </a>
                                                            <?php if ($subject['bookCount'] == 0): ?>
                                                                <button type="button"
                                                                        class="btn btn-sm btn-danger delete-subject"
                                                                        data-id="<?= htmlspecialchars($subject['id']) ?>"
                                                                        data-name="<?= htmlspecialchars($subject['name']) ?>"
                                                                        title="Delete Subject">
                                                                    🗑️
                                                                </button>
                                                            <?php else: ?>
                                                                <button type="button"
                                                                        class="btn btn-sm btn-outline"
                                                                        disabled
                                                                        title="Cannot delete - has <?= $subject['bookCount'] ?> books">
                                                                    🔒
                                                                </button>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <?php if ($total_subjects > $per_page): ?>
                                    <div class="pagination-wrapper">
                                        <?php
                                        $total_pages = ceil($total_subjects / $per_page);
                                        $query_params = $_GET;
                                        ?>
                                        <div class="pagination">
                                            <?php if ($page > 1): ?>
                                                <?php $query_params['page'] = $page - 1; ?>
                                                <a href="?<?= http_build_query($query_params) ?>" class="pagination-btn">‹ Previous</a>
                                            <?php endif; ?>

                                            <?php
                                            $start_page = max(1, $page - 2);
                                            $end_page = min($total_pages, $page + 2);

                                            for ($i = $start_page; $i <= $end_page; $i++):
                                                $query_params['page'] = $i;
                                            ?>
                                                <a href="?<?= http_build_query($query_params) ?>"
                                                   class="pagination-btn <?= $i === $page ? 'active' : '' ?>">
                                                    <?= $i ?>
                                                </a>
                                            <?php endfor; ?>

                                            <?php if ($page < $total_pages): ?>
                                                <?php $query_params['page'] = $page + 1; ?>
                                                <a href="?<?= http_build_query($query_params) ?>" class="pagination-btn">Next ›</a>
                                            <?php endif; ?>
                                        </div>
                                        <div class="pagination-info">
                                            Showing <?= number_format(($page - 1) * $per_page + 1) ?> to
                                            <?= number_format(min($page * $per_page, $total_subjects)) ?> of
                                            <?= number_format($total_subjects) ?> subjects
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                <?php elseif ($action === 'add' || $action === 'edit'): ?>
                    <!-- Add/Edit Subject Form -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <h2 class="page-title"><?= $action === 'add' ? 'Add New Subject' : 'Edit Subject' ?></h2>
                            <p class="page-subtitle">
                                <?= $action === 'add' ? 'Enter subject information to organize books' : 'Update subject information' ?>
                            </p>
                        </div>
                        <div class="page-header-actions">
                            <a href="?" class="btn btn-secondary">
                                <span style="margin-right: 0.5rem;">←</span>
                                Back to Subjects
                            </a>
                        </div>
                    </div>

                    <form method="POST" class="subject-form">
                        <input type="hidden" name="action" value="<?= $action ?>">
                        <?php if ($action === 'edit' && $subject_data): ?>
                            <input type="hidden" name="id" value="<?= htmlspecialchars($subject_data['id']) ?>">
                        <?php endif; ?>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Subject Information</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="name" class="label required">Subject Name</label>
                                        <input type="text"
                                               id="name"
                                               name="name"
                                               class="input"
                                               placeholder="Enter subject name"
                                               value="<?= htmlspecialchars($subject_data['name'] ?? '') ?>"
                                               required>
                                        <div class="help-text">The primary name of the subject (required)</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="nameNepali" class="label">Nepali/Maithili Name</label>
                                        <input type="text"
                                               id="nameNepali"
                                               name="nameNepali"
                                               class="input nepali-input"
                                               placeholder="विषयको नाम"
                                               value="<?= htmlspecialchars($subject_data['nameNepali'] ?? '') ?>">
                                        <div class="help-text">Subject name in Nepali/Maithili script (optional)</div>
                                    </div>
                                </div>

                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="categoryId" class="label required">Category</label>
                                        <select id="categoryId" name="categoryId" class="input" required>
                                            <option value="">Select Category</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?= htmlspecialchars($category['id']) ?>"
                                                        <?= ($subject_data['categoryId'] ?? '') === $category['id'] ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($category['name']) ?>
                                                    <?php if (!empty($category['nameNepali'])): ?>
                                                        (<?= htmlspecialchars($category['nameNepali']) ?>)
                                                    <?php endif; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="help-text">Select the category this subject belongs to (required)</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="code" class="label">Subject Code</label>
                                        <input type="text"
                                               id="code"
                                               name="code"
                                               class="input"
                                               placeholder="e.g., MATH, SCI, LIT"
                                               value="<?= htmlspecialchars($subject_data['code'] ?? '') ?>">
                                        <div class="help-text">Short code for the subject (optional)</div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="description" class="label">Description</label>
                                    <textarea id="description"
                                              name="description"
                                              class="textarea"
                                              rows="4"
                                              placeholder="Enter subject description..."><?= htmlspecialchars($subject_data['description'] ?? '') ?></textarea>
                                    <div class="help-text">Brief description of the subject (optional)</div>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox"
                                               name="isActive"
                                               value="1"
                                               <?= ($subject_data['isActive'] ?? 1) ? 'checked' : '' ?>>
                                        <span class="checkbox-text">Active Subject</span>
                                    </label>
                                    <div class="help-text">Uncheck to deactivate this subject</div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <span style="margin-right: 0.5rem;">💾</span>
                                <?= $action === 'add' ? 'Add Subject' : 'Update Subject' ?>
                            </button>
                            <a href="?" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Confirm Delete</h3>
                <button type="button" class="modal-close" onclick="closeDeleteModal()">×</button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the subject "<span id="deleteSubjectName"></span>"?</p>
                <p class="text-warning">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeDeleteModal()">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">Delete Subject</button>
            </div>
        </div>
    </div>

    <script>
        // Mobile Menu and Delete functionality
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            sidebar.classList.toggle('open');
            overlay.classList.toggle('active');
            document.body.style.overflow = sidebar.classList.contains('open') ? 'hidden' : '';
        }

        function closeMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            sidebar.classList.remove('open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        let deleteSubjectId = null;

        function openDeleteModal(id, name) {
            deleteSubjectId = id;
            document.getElementById('deleteSubjectName').textContent = name;
            document.getElementById('deleteModal').style.display = 'flex';
        }

        function closeDeleteModal() {
            deleteSubjectId = null;
            document.getElementById('deleteModal').style.display = 'none';
        }

        function confirmDelete() {
            if (deleteSubjectId) {
                fetch(`?ajax_delete=1&id=${encodeURIComponent(deleteSubjectId)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('Error: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while deleting the subject.');
                    });
            }
            closeDeleteModal();
        }

        document.addEventListener('DOMContentLoaded', () => {
            // Event listeners
            document.getElementById('mobile-menu-toggle')?.addEventListener('click', toggleMobileMenu);
            document.getElementById('mobile-overlay')?.addEventListener('click', closeMobileMenu);
            document.getElementById('mobile-close-btn')?.addEventListener('click', closeMobileMenu);

            document.querySelectorAll('.delete-subject').forEach(button => {
                button.addEventListener('click', function() {
                    openDeleteModal(this.getAttribute('data-id'), this.getAttribute('data-name'));
                });
            });

            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth <= 1023) closeMobileMenu();
                });
            });

            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    closeDeleteModal();
                    closeMobileMenu();
                }
            });
        });

        window.addEventListener('resize', () => {
            if (window.innerWidth > 1023) closeMobileMenu();
        });
    </script>
</body>
</html>
