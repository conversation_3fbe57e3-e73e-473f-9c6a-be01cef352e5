<?php
require_once '../config/config.php';
require_once '../includes/functions.php';
require_once '../includes/Database.php';

// Check if user is logged in as admin
require_admin();

$db = new Database();
$error = '';
$success = '';
$action = $_GET['action'] ?? 'upload';
$job_id = $_GET['job_id'] ?? null;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if ($_POST['action'] === 'upload') {
            // Check if tables exist first
            if (!$db->tableExists('image_import_jobs') || !$db->tableExists('image_import_files')) {
                throw new Exception('Image import tables do not exist. Please run the setup first.');
            }

            // Handle image upload
            if (!isset($_FILES['image_files']) || empty($_FILES['image_files']['tmp_name'][0])) {
                throw new Exception('No files uploaded');
            }

            $uploadedFiles = $_FILES['image_files'];
            $matchingMethod = $_POST['matching_method'] ?? 'filename';
            $overwriteExisting = isset($_POST['overwrite_existing']);
            
            // Create import job record
            $jobId = 'img' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
            
            $db->insert('image_import_jobs', [
                'id' => $jobId,
                'userId' => $_SESSION['admin_user_id'],
                'matchingMethod' => $matchingMethod,
                'overwriteExisting' => $overwriteExisting ? 1 : 0,
                'status' => 'PENDING',
                'createdAt' => date('Y-m-d H:i:s'),
                'updatedAt' => date('Y-m-d H:i:s')
            ]);

            // Process uploaded files
            $uploadDir = UPLOAD_DIR . 'temp_images/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            $fileCount = 0;
            $totalFiles = count($uploadedFiles['name']);
            
            for ($i = 0; $i < $totalFiles; $i++) {
                if ($uploadedFiles['error'][$i] === UPLOAD_ERR_OK) {
                    $originalName = $uploadedFiles['name'][$i];
                    $tempPath = $uploadedFiles['tmp_name'][$i];
                    
                    // Validate file type
                    if (!is_allowed_image($originalName)) {
                        continue;
                    }
                    
                    // Generate unique filename
                    $fileName = $jobId . '_' . $i . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '', $originalName);
                    $filePath = $uploadDir . $fileName;
                    
                    if (move_uploaded_file($tempPath, $filePath)) {
                        // Store file info in database
                        $db->insert('image_import_files', [
                            'jobId' => $jobId,
                            'originalName' => $originalName,
                            'filePath' => $filePath,
                            'status' => 'PENDING',
                            'createdAt' => date('Y-m-d H:i:s')
                        ]);
                        $fileCount++;
                    }
                }
            }

            // Update job with file count
            $db->update('image_import_jobs', ['totalFiles' => $fileCount], ['id' => $jobId]);

            $success = "Successfully uploaded $fileCount files. Processing will begin shortly.";
            header("Location: import-images.php?action=status&job_id=" . urlencode($jobId));
            exit;
            
        } elseif ($_POST['action'] === 'process' && $job_id) {
            // Start processing job
            $db->update('image_import_jobs', [
                'status' => 'PROCESSING',
                'startTime' => date('Y-m-d H:i:s'),
                'updatedAt' => date('Y-m-d H:i:s')
            ], ['id' => $job_id]);
            
            // Process in background (simplified for demo)
            processImageImportJob($job_id);
            
            header("Location: import-images.php?action=status&job_id=" . urlencode($job_id));
            exit;
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Check if image import tables exist
$tablesExist = $db->tableExists('image_import_jobs') && $db->tableExists('image_import_files');

// Get import job details if viewing status
$importJob = null;
if ($action === 'status' && $job_id && $tablesExist) {
    $importJob = $db->fetch("SELECT * FROM image_import_jobs WHERE id = ?", [$job_id]);
    if ($importJob) {
        // Get associated files
        $importFiles = $db->fetchAll("SELECT * FROM image_import_files WHERE jobId = ?", [$job_id]);
        $importJob['files'] = $importFiles;
    }
}

// Get recent import jobs
$recentJobs = [];
if ($tablesExist) {
    $recentJobs = $db->fetchAll("
        SELECT * FROM image_import_jobs
        ORDER BY createdAt DESC
        LIMIT 10
    ");
}

/**
 * Process image import job
 */
function processImageImportJob($jobId) {
    global $db;

    try {
        $job = $db->fetch("SELECT * FROM image_import_jobs WHERE id = ?", [$jobId]);
        if (!$job) {
            throw new Exception('Job not found');
        }

        $files = $db->fetchAll("SELECT * FROM image_import_files WHERE jobId = ?", [$jobId]);
        $stats = [
            'processed' => 0,
            'matched' => 0,
            'errors' => 0,
            'skipped' => 0
        ];
        
        foreach ($files as $file) {
            try {
                $stats['processed']++;
                
                // Extract accession number from filename
                $accessionNo = extractAccessionFromFilename($file['originalName'], $job['matchingMethod']);
                
                if (!$accessionNo) {
                    $stats['skipped']++;
                    $db->update('image_import_files', [
                        'status' => 'SKIPPED',
                        'errorMessage' => 'Could not extract accession number from filename'
                    ], ['id' => $file['id']]);
                    continue;
                }
                
                // Find book by accession number
                $book = $db->fetch("SELECT id FROM books WHERE accessionNo = ? AND isDeleted = 0", [$accessionNo]);
                
                if (!$book) {
                    $stats['skipped']++;
                    $db->update('image_import_files', [
                        'status' => 'SKIPPED',
                        'errorMessage' => "Book with accession number '$accessionNo' not found"
                    ], ['id' => $file['id']]);
                    continue;
                }
                
                // Check if image already exists
                $existingImage = get_book_image_url($book['id'], false);
                if ($existingImage && !$job['overwriteExisting']) {
                    $stats['skipped']++;
                    $db->update('image_import_files', [
                        'status' => 'SKIPPED',
                        'errorMessage' => 'Image already exists and overwrite is disabled'
                    ], ['id' => $file['id']]);
                    continue;
                }
                
                // Process and save image
                $imageFile = [
                    'tmp_name' => $file['filePath'],
                    'name' => $file['originalName'],
                    'error' => UPLOAD_ERR_OK,
                    'size' => filesize($file['filePath'])
                ];
                
                $result = upload_book_image($imageFile, $book['id']);
                
                if ($result['success']) {
                    $stats['matched']++;
                    $db->update('image_import_files', [
                        'status' => 'COMPLETED',
                        'bookId' => $book['id'],
                        'accessionNo' => $accessionNo
                    ], ['id' => $file['id']]);
                } else {
                    $stats['errors']++;
                    $db->update('image_import_files', [
                        'status' => 'ERROR',
                        'errorMessage' => $result['error']
                    ], ['id' => $file['id']]);
                }
                
            } catch (Exception $e) {
                $stats['errors']++;
                $db->update('image_import_files', [
                    'status' => 'ERROR',
                    'errorMessage' => $e->getMessage()
                ], ['id' => $file['id']]);
            }
        }
        
        // Update job status
        $db->update('image_import_jobs', [
            'status' => 'COMPLETED',
            'processedFiles' => $stats['processed'],
            'matchedFiles' => $stats['matched'],
            'errorFiles' => $stats['errors'],
            'skippedFiles' => $stats['skipped'],
            'endTime' => date('Y-m-d H:i:s'),
            'updatedAt' => date('Y-m-d H:i:s')
        ], ['id' => $jobId]);
        
    } catch (Exception $e) {
        $db->update('image_import_jobs', [
            'status' => 'FAILED',
            'errorMessage' => $e->getMessage(),
            'endTime' => date('Y-m-d H:i:s'),
            'updatedAt' => date('Y-m-d H:i:s')
        ], ['id' => $jobId]);
    }
}

/**
 * Extract accession number from filename
 */
function extractAccessionFromFilename($filename, $method) {
    $basename = pathinfo($filename, PATHINFO_FILENAME);
    
    switch ($method) {
        case 'filename':
            // Look for MVKL pattern first
            if (preg_match('/MVKL\d+(-\d+)?/i', $basename, $matches)) {
                return strtoupper($matches[0]);
            }
            // Look for any number pattern
            if (preg_match('/\d+/', $basename, $matches)) {
                return 'MVKL' . str_pad($matches[0], 3, '0', STR_PAD_LEFT);
            }
            break;
            
        case 'exact':
            // Filename must exactly match accession number
            if (preg_match('/^MVKL\d+(-\d+)?$/i', $basename)) {
                return strtoupper($basename);
            }
            break;
    }
    
    return null;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import Images - मैथिली विकास कोष</title>
    <link rel="stylesheet" href="<?= asset_url('css/admin.css') ?>">
    <link rel="stylesheet" href="<?= asset_url('css/responsive.css') ?>">
    <style>
        .image-drop-zone {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            background: #f9fafb;
            cursor: pointer;
        }

        .image-drop-zone.drag-over {
            border-color: #3b82f6;
            background: #eff6ff;
        }

        .image-drop-zone:hover {
            border-color: #6b7280;
            background: #f3f4f6;
        }

        .image-preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .image-preview-item {
            position: relative;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            overflow: hidden;
            background: white;
        }

        .image-preview-item img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }

        .image-preview-item .filename {
            padding: 0.5rem;
            font-size: 0.75rem;
            color: #6b7280;
            border-top: 1px solid #e5e7eb;
            word-break: break-all;
        }

        .image-preview-item .remove-btn {
            position: absolute;
            top: 4px;
            right: 4px;
            background: rgba(239, 68, 68, 0.9);
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .matching-method-info {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 6px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .file-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .file-stat {
            text-align: center;
            padding: 0.75rem;
            background: #f9fafb;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .file-stat-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
        }

        .file-stat-label {
            font-size: 0.875rem;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar" id="admin-sidebar">
            <div class="sidebar-header">
                <div class="sidebar-brand">
                    <h2 class="brand-title">मैथिली विकास कोष</h2>
                    <p class="brand-subtitle">Maithili Vikas Kosh</p>
                </div>
                <button class="mobile-close-btn" id="mobile-close-btn">
                    <span>×</span>
                </button>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <div class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Dashboard
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Book Management</div>
                    <div class="nav-item">
                        <a href="books.php" class="nav-link">
                            <span class="nav-icon">📚</span>
                            Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="books.php?action=add" class="nav-link">
                            <span class="nav-icon">➕</span>
                            Add Book
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-books.php" class="nav-link">
                            <span class="nav-icon">📥</span>
                            Import Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-images.php" class="nav-link active">
                            <span class="nav-icon">🖼️</span>
                            Import Images
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Catalog Management</div>
                    <div class="nav-item">
                        <a href="authors.php" class="nav-link">
                            <span class="nav-icon">✍️</span>
                            Authors
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="categories.php" class="nav-link">
                            <span class="nav-icon">📂</span>
                            Categories
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="publishers.php" class="nav-link">
                            <span class="nav-icon">🏢</span>
                            Publishers
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="languages.php" class="nav-link">
                            <span class="nav-icon">🌐</span>
                            Languages
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="subjects.php" class="nav-link">
                            <span class="nav-icon">📖</span>
                            Subjects
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="series.php" class="nav-link">
                            <span class="nav-icon">📑</span>
                            Book Series
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="locations.php" class="nav-link">
                            <span class="nav-icon">📍</span>
                            Locations
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="conditions.php" class="nav-link">
                            <span class="nav-icon">🔧</span>
                            Conditions
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="sources.php" class="nav-link">
                            <span class="nav-icon">🎁</span>
                            Sources
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Reports & Analytics</div>
                    <div class="nav-item">
                        <a href="reports.php" class="nav-link">
                            <span class="nav-icon">📈</span>
                            Reports
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="analytics.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Analytics
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <div class="nav-item">
                        <a href="users.php" class="nav-link">
                            <span class="nav-icon">👤</span>
                            Admin Users
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="settings.php" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            Settings
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="backup.php" class="nav-link">
                            <span class="nav-icon">💾</span>
                            Backup & Export
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="../database_reset.php" class="nav-link" style="color: #ef4444;">
                            <span class="nav-icon">🗑️</span>
                            Database Reset
                        </a>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <div class="admin-header">
                <div class="admin-header-content">
                    <div class="admin-header-left">
                        <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                            <span></span>
                            <span></span>
                            <span></span>
                        </button>
                        <h1 class="admin-title">Import Images</h1>
                        <p class="admin-subtitle">मैथिली विकास कोष</p>
                    </div>
                    <div class="admin-header-right">
                        <a href="<?= ADMIN_URL ?>/export.php" class="btn btn-secondary">
                            <span class="btn-icon">📤</span>
                            Export
                        </a>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="admin-content">
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        <span class="alert-icon">✅</span>
                        <?= htmlspecialchars($success) ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <span class="alert-icon">❌</span>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <?php if ($action === 'upload'): ?>
                    <!-- Upload Form -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span style="margin-right: 0.5rem;">🖼️</span>
                                Upload Book Cover Images
                            </h2>
                            <p class="card-description">Upload multiple book cover images to automatically match with books</p>
                        </div>
                        <div class="card-body">
                            <?php if (!$tablesExist): ?>
                                <div class="alert alert-warning">
                                    <span class="alert-icon">⚠️</span>
                                    Image import tables are not set up yet.
                                    <a href="setup_image_import.php" class="btn btn-sm btn-primary" style="margin-left: 1rem;">
                                        Setup Now
                                    </a>
                                </div>
                            <?php else: ?>
                            <form method="POST" enctype="multipart/form-data" class="form" id="image-upload-form">
                                <input type="hidden" name="action" value="upload">

                                <!-- Image Drop Zone -->
                                <div class="form-group">
                                    <label class="form-label">Book Cover Images *</label>
                                    <div class="image-drop-zone" id="image-drop-zone">
                                        <div class="drop-zone-content">
                                            <div style="font-size: 3rem; margin-bottom: 1rem;">🖼️</div>
                                            <h3 style="margin: 0 0 0.5rem 0; color: #374151;">Drop images here or click to browse</h3>
                                            <p style="margin: 0; color: #6b7280; font-size: 0.875rem;">
                                                Supported formats: JPG, PNG, WebP • Max size: <?= MAX_FILE_SIZE / 1024 / 1024 ?>MB per file
                                            </p>
                                        </div>
                                        <input type="file"
                                               id="image_files"
                                               name="image_files[]"
                                               accept="image/jpeg,image/jpg,image/png,image/webp"
                                               multiple
                                               required
                                               style="display: none;">
                                    </div>

                                    <!-- Image Preview Grid -->
                                    <div class="image-preview-grid" id="image-preview-grid" style="display: none;"></div>

                                    <!-- File Statistics -->
                                    <div class="file-stats" id="file-stats" style="display: none;">
                                        <div class="file-stat">
                                            <span class="file-stat-number" id="total-files">0</span>
                                            <span class="file-stat-label">Total Files</span>
                                        </div>
                                        <div class="file-stat">
                                            <span class="file-stat-number" id="valid-files">0</span>
                                            <span class="file-stat-label">Valid Images</span>
                                        </div>
                                        <div class="file-stat">
                                            <span class="file-stat-number" id="total-size">0 MB</span>
                                            <span class="file-stat-label">Total Size</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Matching Method -->
                                <div class="form-group">
                                    <label for="matching_method" class="form-label">Filename Matching Method</label>
                                    <select id="matching_method" name="matching_method" class="form-control">
                                        <option value="filename">Smart matching (extract from filename)</option>
                                        <option value="exact">Exact match (filename = accession number)</option>
                                    </select>
                                    <div class="form-help">
                                        Choose how to match image filenames with book accession numbers.
                                    </div>
                                </div>

                                <!-- Matching Method Info -->
                                <div class="matching-method-info" id="matching-info">
                                    <h4 style="margin: 0 0 0.5rem 0; color: #0369a1;">Smart Matching Examples:</h4>
                                    <ul style="margin: 0; padding-left: 1.25rem; color: #0369a1; font-size: 0.875rem;">
                                        <li><code>MVKL001.jpg</code> → matches book with accession <code>MVKL001</code></li>
                                        <li><code>book_MVKL123_cover.png</code> → matches book with accession <code>MVKL123</code></li>
                                        <li><code>456.jpg</code> → matches book with accession <code>MVKL456</code></li>
                                        <li><code>cover_789.png</code> → matches book with accession <code>MVKL789</code></li>
                                    </ul>
                                </div>

                                <!-- Options -->
                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" name="overwrite_existing" value="1">
                                        <span class="checkbox-text">Overwrite existing images</span>
                                    </label>
                                    <div class="form-help">
                                        If enabled, existing book cover images will be replaced with new uploads.
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary" id="upload-btn" disabled>
                                        <span class="btn-icon">📤</span>
                                        Upload and Process Images
                                    </button>
                                    <a href="books.php" class="btn btn-secondary">
                                        <span class="btn-icon">📚</span>
                                        Back to Books
                                    </a>
                                </div>
                            </form>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($action === 'status' && $importJob): ?>
                    <!-- Import Status -->
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span style="margin-right: 0.5rem;">📊</span>
                                Image Import Status
                            </h2>
                            <p class="card-description">Job ID: <?= htmlspecialchars($importJob['id']) ?></p>
                        </div>
                        <div class="card-body">
                            <div class="import-status">
                                <div class="status-grid">
                                    <div class="status-item">
                                        <span class="status-label">Status:</span>
                                        <span class="status-value status-<?= strtolower($importJob['status']) ?>">
                                            <?= htmlspecialchars($importJob['status']) ?>
                                        </span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Created:</span>
                                        <span class="status-value"><?= date('Y-m-d H:i:s', strtotime($importJob['createdAt'])) ?></span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Matching Method:</span>
                                        <span class="status-value"><?= htmlspecialchars($importJob['matchingMethod']) ?></span>
                                    </div>
                                    <div class="status-item">
                                        <span class="status-label">Overwrite:</span>
                                        <span class="status-value"><?= $importJob['overwriteExisting'] ? 'Yes' : 'No' ?></span>
                                    </div>
                                </div>

                                <?php if ($importJob['status'] === 'PENDING'): ?>
                                    <div class="status-actions">
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="process">
                                            <button type="submit" class="btn btn-primary">
                                                <span class="btn-icon">▶️</span>
                                                Start Processing
                                            </button>
                                        </form>
                                    </div>
                                <?php elseif ($importJob['status'] === 'PROCESSING'): ?>
                                    <div class="status-message">
                                        <span class="status-icon">⏳</span>
                                        Processing images... Please wait.
                                    </div>
                                    <script>
                                        // Auto-refresh every 5 seconds during processing
                                        setTimeout(function() {
                                            window.location.reload();
                                        }, 5000);
                                    </script>
                                <?php elseif ($importJob['status'] === 'COMPLETED'): ?>
                                    <div class="import-stats">
                                        <h3>Import Statistics</h3>
                                        <div class="stats-grid">
                                            <div class="stat-item success">
                                                <span class="stat-number"><?= $importJob['matchedFiles'] ?></span>
                                                <span class="stat-label">Images Matched</span>
                                            </div>
                                            <div class="stat-item warning">
                                                <span class="stat-number"><?= $importJob['skippedFiles'] ?></span>
                                                <span class="stat-label">Files Skipped</span>
                                            </div>
                                            <div class="stat-item error">
                                                <span class="stat-number"><?= $importJob['errorFiles'] ?></span>
                                                <span class="stat-label">Errors</span>
                                            </div>
                                            <div class="stat-item info">
                                                <span class="stat-number"><?= $importJob['totalFiles'] ?></span>
                                                <span class="stat-label">Total Files</span>
                                            </div>
                                        </div>
                                    </div>
                                <?php elseif ($importJob['status'] === 'FAILED'): ?>
                                    <div class="status-error">
                                        <span class="status-icon">❌</span>
                                        <div class="error-details">
                                            <h4>Import Failed</h4>
                                            <?php if ($importJob['errorMessage']): ?>
                                                <p><?= htmlspecialchars($importJob['errorMessage']) ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="card-actions">
                                <a href="import-images.php" class="btn btn-secondary">
                                    <span class="btn-icon">🖼️</span>
                                    New Import
                                </a>
                                <a href="books.php" class="btn btn-primary">
                                    <span class="btn-icon">📚</span>
                                    View Books
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Recent Import Jobs -->
                <?php if (!empty($recentJobs)): ?>
                    <div class="card">
                        <div class="card-header">
                            <h2 class="card-title">
                                <span style="margin-right: 0.5rem;">📋</span>
                                Recent Image Import Jobs
                            </h2>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Job ID</th>
                                            <th>Status</th>
                                            <th>Matched</th>
                                            <th>Errors</th>
                                            <th>Total Files</th>
                                            <th>Created</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recentJobs as $job): ?>
                                            <tr>
                                                <td>
                                                    <code><?= htmlspecialchars($job['id']) ?></code>
                                                </td>
                                                <td>
                                                    <span class="status-badge status-<?= strtolower($job['status']) ?>">
                                                        <?= htmlspecialchars($job['status']) ?>
                                                    </span>
                                                </td>
                                                <td><?= $job['matchedFiles'] ?? 0 ?></td>
                                                <td><?= $job['errorFiles'] ?? 0 ?></td>
                                                <td><?= $job['totalFiles'] ?? 0 ?></td>
                                                <td><?= date('M j, Y H:i', strtotime($job['createdAt'])) ?></td>
                                                <td>
                                                    <a href="import-images.php?action=status&job_id=<?= urlencode($job['id']) ?>"
                                                       class="btn btn-sm btn-secondary">
                                                        View
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Usage Guide -->
                <div class="card">
                    <div class="card-header">
                        <h2 class="card-title">
                            <span style="margin-right: 0.5rem;">📖</span>
                            Image Import Guide
                        </h2>
                    </div>
                    <div class="card-body">
                        <div class="format-guide">
                            <h3>Filename Conventions</h3>
                            <div style="background: #eff6ff; padding: 1rem; border-radius: 0.5rem; border: 1px solid #dbeafe; margin-bottom: 1.5rem;">
                                <h4 style="font-weight: 600; color: #1e40af; margin: 0 0 0.5rem 0;">Smart Matching (Recommended)</h4>
                                <ul style="font-size: 0.875rem; color: #1e40af; margin: 0; padding-left: 1.25rem; line-height: 1.5;">
                                    <li><strong>MVKL001.jpg</strong> - Direct accession number match</li>
                                    <li><strong>book_MVKL123_cover.png</strong> - Extracts MVKL123</li>
                                    <li><strong>456.jpg</strong> - Converts to MVKL456</li>
                                    <li><strong>cover_789.png</strong> - Converts to MVKL789</li>
                                    <li><strong>MVKL100-02.jpg</strong> - Matches copy numbers</li>
                                </ul>
                            </div>

                            <div style="background: #fef2f2; padding: 1rem; border-radius: 0.5rem; border: 1px solid #fecaca; margin-bottom: 1.5rem;">
                                <h4 style="font-weight: 600; color: #991b1b; margin: 0 0 0.5rem 0;">Exact Matching</h4>
                                <ul style="font-size: 0.875rem; color: #dc2626; margin: 0; padding-left: 1.25rem; line-height: 1.5;">
                                    <li><strong>MVKL001.jpg</strong> - Filename must exactly match accession number</li>
                                    <li><strong>MVKL123.png</strong> - No additional text allowed</li>
                                    <li><strong>MVKL100-02.webp</strong> - Copy numbers supported</li>
                                </ul>
                            </div>

                            <h3>Tips for Best Results</h3>
                            <ul style="font-size: 0.875rem; color: #374151; line-height: 1.6; padding-left: 1.25rem;">
                                <li>Use high-quality images (minimum 300x400 pixels recommended)</li>
                                <li>Images will be automatically resized and compressed to ~30KB JPEG format</li>
                                <li>Supported formats: JPG, PNG, WebP</li>
                                <li>Maximum file size: <?= MAX_FILE_SIZE / 1024 / 1024 ?>MB per image</li>
                                <li>Ensure book records exist before uploading images</li>
                                <li>Use consistent naming conventions for easier matching</li>
                                <li>Enable "Overwrite existing" to replace current book cover images</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <!-- Scripts -->
    <script src="<?= asset_url('js/admin.js') ?>"></script>

    <script>
        // Image upload functionality
        let selectedFiles = [];

        document.addEventListener('DOMContentLoaded', function() {
            const dropZone = document.getElementById('image-drop-zone');
            const fileInput = document.getElementById('image_files');
            const previewGrid = document.getElementById('image-preview-grid');
            const fileStats = document.getElementById('file-stats');
            const uploadBtn = document.getElementById('upload-btn');
            const matchingSelect = document.getElementById('matching_method');
            const matchingInfo = document.getElementById('matching-info');

            // Click to browse
            dropZone.addEventListener('click', () => fileInput.click());

            // Drag and drop events
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('drag-over');
            });

            dropZone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                dropZone.classList.remove('drag-over');
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('drag-over');
                handleFiles(e.dataTransfer.files);
            });

            // File input change
            fileInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
            });

            // Matching method change
            matchingSelect.addEventListener('change', updateMatchingInfo);

            function handleFiles(files) {
                selectedFiles = Array.from(files).filter(file => {
                    return file.type.startsWith('image/') &&
                           ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'].includes(file.type);
                });

                updateFileInput();
                updatePreview();
                updateStats();
                updateUploadButton();
            }

            function updateFileInput() {
                // Create new FileList for the input
                const dt = new DataTransfer();
                selectedFiles.forEach(file => dt.items.add(file));
                fileInput.files = dt.files;
            }

            function updatePreview() {
                previewGrid.innerHTML = '';

                if (selectedFiles.length === 0) {
                    previewGrid.style.display = 'none';
                    return;
                }

                previewGrid.style.display = 'grid';

                selectedFiles.forEach((file, index) => {
                    const item = document.createElement('div');
                    item.className = 'image-preview-item';

                    const img = document.createElement('img');
                    img.src = URL.createObjectURL(file);
                    img.onload = () => URL.revokeObjectURL(img.src);

                    const filename = document.createElement('div');
                    filename.className = 'filename';
                    filename.textContent = file.name;

                    const removeBtn = document.createElement('button');
                    removeBtn.className = 'remove-btn';
                    removeBtn.innerHTML = '×';
                    removeBtn.type = 'button';
                    removeBtn.onclick = () => removeFile(index);

                    item.appendChild(img);
                    item.appendChild(filename);
                    item.appendChild(removeBtn);
                    previewGrid.appendChild(item);
                });
            }

            function removeFile(index) {
                selectedFiles.splice(index, 1);
                updateFileInput();
                updatePreview();
                updateStats();
                updateUploadButton();
            }

            function updateStats() {
                if (selectedFiles.length === 0) {
                    fileStats.style.display = 'none';
                    return;
                }

                fileStats.style.display = 'grid';

                const totalSize = selectedFiles.reduce((sum, file) => sum + file.size, 0);
                const validFiles = selectedFiles.length;

                document.getElementById('total-files').textContent = selectedFiles.length;
                document.getElementById('valid-files').textContent = validFiles;
                document.getElementById('total-size').textContent = (totalSize / 1024 / 1024).toFixed(1) + ' MB';
            }

            function updateUploadButton() {
                uploadBtn.disabled = selectedFiles.length === 0;
            }

            function updateMatchingInfo() {
                const method = matchingSelect.value;
                const examples = {
                    'filename': [
                        '<code>MVKL001.jpg</code> → matches book with accession <code>MVKL001</code>',
                        '<code>book_MVKL123_cover.png</code> → matches book with accession <code>MVKL123</code>',
                        '<code>456.jpg</code> → matches book with accession <code>MVKL456</code>',
                        '<code>cover_789.png</code> → matches book with accession <code>MVKL789</code>'
                    ],
                    'exact': [
                        '<code>MVKL001.jpg</code> → matches book with accession <code>MVKL001</code>',
                        '<code>MVKL123.png</code> → matches book with accession <code>MVKL123</code>',
                        '<code>book_cover.jpg</code> → no match (not exact accession number)'
                    ]
                };

                const title = method === 'filename' ? 'Smart Matching Examples:' : 'Exact Matching Examples:';
                const exampleList = examples[method].map(ex => `<li>${ex}</li>`).join('');

                matchingInfo.innerHTML = `
                    <h4 style="margin: 0 0 0.5rem 0; color: #0369a1;">${title}</h4>
                    <ul style="margin: 0; padding-left: 1.25rem; color: #0369a1; font-size: 0.875rem;">
                        ${exampleList}
                    </ul>
                `;
            }

            // Initialize
            updateMatchingInfo();
        });
    </script>
</body>
</html>
