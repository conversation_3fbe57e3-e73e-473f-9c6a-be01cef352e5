<?php
/**
 * मैथिली विकास कोष - <PERSON><PERSON><PERSON>
 * Languages Management
 */

require_once '../config/config.php';
require_once '../includes/Database.php';
require_once '../includes/functions.php';

// Check if user is logged in
require_admin();

// Initialize database
$db = new Database();

// Handle actions
$action = $_GET['action'] ?? 'list';
$id = $_GET['id'] ?? null;
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
            case 'edit':
                $name = trim($_POST['name'] ?? '');
                $nameNepali = trim($_POST['nameNepali'] ?? '') ?: null;
                $code = trim($_POST['code'] ?? '') ?: null;
                $isActive = isset($_POST['isActive']) ? 1 : 0;
                
                // Validation
                if (empty($name)) {
                    $error = 'Language name is required';
                } elseif (!empty($code) && !preg_match('/^[a-z]{2,3}$/', $code)) {
                    $error = 'Language code must be 2-3 lowercase letters';
                } else {
                    try {
                        if ($_POST['action'] === 'add') {
                            // Check if language already exists
                            $existing = $db->fetch("SELECT id FROM languages WHERE name = ? AND isDeleted = 0", [$name]);
                            if ($existing) {
                                $error = 'Language with this name already exists';
                            } elseif (!empty($code)) {
                                $existing_code = $db->fetch("SELECT id FROM languages WHERE code = ? AND isDeleted = 0", [$code]);
                                if ($existing_code) {
                                    $error = 'Language with this code already exists';
                                }
                            }
                            
                            if (!$error) {
                                $language_id = 'lang' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
                                $data = [
                                    'id' => $language_id,
                                    'name' => $name,
                                    'nameNepali' => $nameNepali,
                                    'code' => $code,
                                    'isActive' => $isActive,
                                    'createdAt' => date('Y-m-d H:i:s'),
                                    'updatedAt' => date('Y-m-d H:i:s')
                                ];
                                $db->insert('languages', $data);
                                $message = 'Language added successfully';
                                log_activity("Added language: $name", 'INFO');
                                $action = 'list';
                            }
                        } else {
                            // Edit existing language
                            $edit_id = $_POST['id'] ?? '';
                            if ($edit_id) {
                                // Check if name conflicts with other languages
                                $existing = $db->fetch("SELECT id FROM languages WHERE name = ? AND id != ? AND isDeleted = 0", [$name, $edit_id]);
                                if ($existing) {
                                    $error = 'Language with this name already exists';
                                } elseif (!empty($code)) {
                                    $existing_code = $db->fetch("SELECT id FROM languages WHERE code = ? AND id != ? AND isDeleted = 0", [$code, $edit_id]);
                                    if ($existing_code) {
                                        $error = 'Language with this code already exists';
                                    }
                                }
                                
                                if (!$error) {
                                    $data = [
                                        'name' => $name,
                                        'nameNepali' => $nameNepali,
                                        'code' => $code,
                                        'isActive' => $isActive,
                                        'updatedAt' => date('Y-m-d H:i:s')
                                    ];
                                    $db->update('languages', $data, 'id = ?', [$edit_id]);
                                    $message = 'Language updated successfully';
                                    log_activity("Updated language: $name", 'INFO');
                                    $action = 'list';
                                }
                            }
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                        log_activity("Language save error: " . $e->getMessage(), 'ERROR');
                    }
                }
                break;
                
            case 'delete':
                $delete_id = $_POST['id'] ?? '';
                if ($delete_id) {
                    try {
                        // Check if language has books
                        $book_count = $db->fetchColumn("SELECT COUNT(*) FROM books WHERE languageId = ? AND isDeleted = 0", [$delete_id]);
                        if ($book_count > 0) {
                            $error = "Cannot delete language. $book_count books are associated with this language.";
                        } else {
                            $language_name = $db->fetchColumn("SELECT name FROM languages WHERE id = ?", [$delete_id]);
                            $db->delete('languages', 'id = ?', [$delete_id]);
                            $message = 'Language deleted successfully';
                            log_activity("Deleted language: $language_name", 'INFO');
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                        log_activity("Language delete error: " . $e->getMessage(), 'ERROR');
                    }
                }
                break;
        }
    }
}

// Handle AJAX delete request
if (isset($_GET['ajax_delete']) && $_GET['ajax_delete'] === '1' && $id) {
    header('Content-Type: application/json');
    try {
        // Check if language has books
        $book_count = $db->fetchColumn("SELECT COUNT(*) FROM books WHERE languageId = ? AND isDeleted = 0", [$id]);
        if ($book_count > 0) {
            echo json_encode(['success' => false, 'message' => "Cannot delete language. $book_count books are associated with this language."]);
        } else {
            $language_name = $db->fetchColumn("SELECT name FROM languages WHERE id = ?", [$id]);
            $db->delete('languages', 'id = ?', [$id]);
            log_activity("Deleted language: $language_name", 'INFO');
            echo json_encode(['success' => true, 'message' => 'Language deleted successfully']);
        }
    } catch (Exception $e) {
        log_activity("Language delete error: " . $e->getMessage(), 'ERROR');
        echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
    }
    exit;
}

// Get data for edit form
$language_data = null;
if ($action === 'edit' && $id) {
    $language_data = $db->fetch("SELECT * FROM languages WHERE id = ? AND isDeleted = 0", [$id]);
    if (!$language_data) {
        $error = 'Language not found';
        $action = 'list';
    }
}

// Get languages list for list view
$languages = [];
$total_languages = 0;
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = ADMIN_ITEMS_PER_PAGE;
$search = trim($_GET['search'] ?? '');
$status_filter = $_GET['status'] ?? '';

if ($action === 'list') {
    try {
        $where_conditions = ['l.isDeleted = 0'];
        $params = [];

        if (!empty($search)) {
            $where_conditions[] = '(l.name LIKE ? OR l.nameNepali LIKE ? OR l.code LIKE ?)';
            $search_param = "%$search%";
            $params[] = $search_param;
            $params[] = $search_param;
            $params[] = $search_param;
        }

        if ($status_filter === 'active') {
            $where_conditions[] = 'l.isActive = 1';
        } elseif ($status_filter === 'inactive') {
            $where_conditions[] = 'l.isActive = 0';
        }

        $where_clause = implode(' AND ', $where_conditions);

        // Get total count
        $total_languages = $db->fetchColumn("
            SELECT COUNT(*)
            FROM languages l
            WHERE $where_clause
        ", $params);

        // Get languages with book count
        $offset = ($page - 1) * $per_page;
        $languages = $db->fetchAll("
            SELECT l.*,
                   COUNT(b.id) as bookCount
            FROM languages l
            LEFT JOIN books b ON l.id = b.languageId AND b.isDeleted = 0
            WHERE $where_clause
            GROUP BY l.id
            ORDER BY l.name ASC
            LIMIT $per_page OFFSET $offset
        ", $params);
        
    } catch (Exception $e) {
        $error = 'Database error: ' . $e->getMessage();
        log_activity("Languages list error: " . $e->getMessage(), 'ERROR');
    }
}

$page_title = 'Languages - ' . APP_NAME;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($page_title) ?></title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="<?= asset_url('css/style.css') ?>">
    <link rel="stylesheet" href="<?= asset_url('css/admin.css') ?>">
    
    <!-- Favicon and App Icons -->
    <?= generate_favicon_tags() ?>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar">
            <div class="sidebar-header">
                <button class="mobile-close-btn" id="mobile-close-btn">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
                <div class="sidebar-logo">
                    <img src="<?= icon_url(48) ?>" alt="<?= APP_NAME ?> Icon" class="sidebar-logo-image">
                </div>
                <h2 class="sidebar-title">Admin Panel</h2>
                <p class="sidebar-subtitle">मैथिली विकास कोष</p>
            </div>

            <!-- Mobile User Section -->
            <div class="mobile-user-section">
                <div class="mobile-user-info">
                    <div class="mobile-user-avatar">
                        <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                    </div>
                    <div class="mobile-user-details">
                        <span class="mobile-user-name"><?= htmlspecialchars($_SESSION['admin_name']) ?></span>
                        <span class="mobile-user-role">Administrator</span>
                    </div>
                </div>
                <a href="logout.php" class="mobile-logout-btn">
                    <span class="logout-icon">🚪</span>
                    Logout
                </a>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <div class="nav-item">
                        <a href="dashboard.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Dashboard
                        </a>
                    </div>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Book Management</div>
                    <div class="nav-item">
                        <a href="books.php" class="nav-link">
                            <span class="nav-icon">📚</span>
                            Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="books.php?action=add" class="nav-link">
                            <span class="nav-icon">➕</span>
                            Add Book
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-books.php" class="nav-link">
                            <span class="nav-icon">📥</span>
                            Import Books
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="import-images.php" class="nav-link">
                            <span class="nav-icon">🖼️</span>
                            Import Images
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Catalog Management</div>
                    <div class="nav-item">
                        <a href="authors.php" class="nav-link">
                            <span class="nav-icon">✍️</span>
                            Authors
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="categories.php" class="nav-link">
                            <span class="nav-icon">🏷️</span>
                            Categories
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="publishers.php" class="nav-link">
                            <span class="nav-icon">🏢</span>
                            Publishers
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="languages.php" class="nav-link active">
                            <span class="nav-icon">🌐</span>
                            Languages
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="subjects.php" class="nav-link">
                            <span class="nav-icon">📖</span>
                            Subjects
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="series.php" class="nav-link">
                            <span class="nav-icon">📑</span>
                            Book Series
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="locations.php" class="nav-link">
                            <span class="nav-icon">📍</span>
                            Locations
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="conditions.php" class="nav-link">
                            <span class="nav-icon">🔧</span>
                            Conditions
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="sources.php" class="nav-link">
                            <span class="nav-icon">🎁</span>
                            Sources
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Reports & Analytics</div>
                    <div class="nav-item">
                        <a href="reports.php" class="nav-link">
                            <span class="nav-icon">📈</span>
                            Collection Reports
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="analytics.php" class="nav-link">
                            <span class="nav-icon">📊</span>
                            Analytics
                        </a>
                    </div>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">System</div>
                    <div class="nav-item">
                        <a href="users.php" class="nav-link">
                            <span class="nav-icon">👤</span>
                            Admin Users
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="settings.php" class="nav-link">
                            <span class="nav-icon">⚙️</span>
                            Settings
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="backup.php" class="nav-link">
                            <span class="nav-icon">💾</span>
                            Backup & Export
                        </a>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <header class="admin-header">
                <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="header-title">
                            <span class="title-icon">🌐</span>
                            Languages Management
                        </h1>
                        <p class="header-subtitle">Manage book languages and language codes</p>
                    </div>
                    <div class="header-actions">
                        <div class="user-info">
                            <span class="welcome-text">Welcome, <?= htmlspecialchars($_SESSION['admin_name']) ?>!</span>
                            <div class="user-menu">
                                <div class="user-avatar">
                                    <?= strtoupper(substr($_SESSION['admin_name'], 0, 1)) ?>
                                </div>
                            </div>
                        </div>
                        <a href="logout.php" class="btn btn-secondary btn-sm">
                            <span style="margin-right: 0.5rem;">🚪</span>
                            Logout
                        </a>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Breadcrumb -->
                <nav class="breadcrumb">
                    <a href="dashboard.php" class="breadcrumb-item">Dashboard</a>
                    <span class="breadcrumb-separator">›</span>
                    <span class="breadcrumb-item active">Languages</span>
                </nav>

                <!-- Messages -->
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <span class="alert-icon">✅</span>
                        <?= htmlspecialchars($message) ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <span class="alert-icon">❌</span>
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>

                <?php if ($action === 'list'): ?>
                    <!-- Languages List -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <h2 class="page-title">Languages</h2>
                            <p class="page-subtitle">Manage book languages and language codes</p>
                        </div>
                        <div class="page-header-actions">
                            <a href="?action=add" class="btn btn-primary">
                                <span style="margin-right: 0.5rem;">➕</span>
                                Add Language
                            </a>
                        </div>
                    </div>

                    <!-- Search and Filters -->
                    <div class="card">
                        <div class="card-body">
                            <form method="GET" class="search-form">
                                <div class="search-form-row">
                                    <div class="search-input-group">
                                        <input type="text" name="search" value="<?= htmlspecialchars($search) ?>"
                                               placeholder="Search languages by name or code..." class="input">
                                        <select name="status" class="input">
                                            <option value="">All Status</option>
                                            <option value="active" <?= $status_filter === 'active' ? 'selected' : '' ?>>Active</option>
                                            <option value="inactive" <?= $status_filter === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                        </select>
                                        <button type="submit" class="btn btn-secondary">
                                            <span style="margin-right: 0.5rem;">🔍</span>
                                            Search
                                        </button>
                                    </div>
                                    <?php if (!empty($search) || !empty($status_filter)): ?>
                                        <a href="?" class="btn btn-outline">Clear</a>
                                    <?php endif; ?>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Languages Table -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                Languages List
                                <?php if (!empty($search)): ?>
                                    <span class="text-muted">(Search: "<?= htmlspecialchars($search) ?>")</span>
                                <?php endif; ?>
                                <?php if (!empty($status_filter)): ?>
                                    <span class="text-muted">(Status: <?= ucfirst($status_filter) ?>)</span>
                                <?php endif; ?>
                            </h3>
                            <div class="card-header-actions">
                                <span class="text-muted">Total: <?= number_format($total_languages) ?> languages</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <?php if (empty($languages)): ?>
                                <div class="empty-state">
                                    <div class="empty-state-icon">🌐</div>
                                    <h3 class="empty-state-title">No Languages Found</h3>
                                    <p class="empty-state-description">
                                        <?php if (!empty($search) || !empty($status_filter)): ?>
                                            No languages match your search criteria. Try adjusting your search terms or filters.
                                        <?php else: ?>
                                            Start by adding your first language to the library system.
                                        <?php endif; ?>
                                    </p>
                                    <div class="empty-state-actions">
                                        <?php if (!empty($search) || !empty($status_filter)): ?>
                                            <a href="?" class="btn btn-secondary">Clear Filters</a>
                                        <?php endif; ?>
                                        <a href="?action=add" class="btn btn-primary">Add First Language</a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Nepali Name</th>
                                                <th>Code</th>
                                                <th>Books</th>
                                                <th>Status</th>
                                                <th>Created</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($languages as $language): ?>
                                                <tr>
                                                    <td>
                                                        <strong><?= htmlspecialchars($language['name']) ?></strong>
                                                    </td>
                                                    <td>
                                                        <?php if (!empty($language['nameNepali'])): ?>
                                                            <span class="nepali-text"><?= htmlspecialchars($language['nameNepali']) ?></span>
                                                        <?php else: ?>
                                                            <span class="text-muted">—</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if (!empty($language['code'])): ?>
                                                            <code class="language-code"><?= htmlspecialchars($language['code']) ?></code>
                                                        <?php else: ?>
                                                            <span class="text-muted">—</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-info"><?= number_format($language['bookCount']) ?> books</span>
                                                    </td>
                                                    <td>
                                                        <?php if ($language['isActive']): ?>
                                                            <span class="badge badge-success">Active</span>
                                                        <?php else: ?>
                                                            <span class="badge badge-warning">Inactive</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="text-muted"><?= date('M j, Y', strtotime($language['createdAt'])) ?></span>
                                                    </td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <a href="?action=edit&id=<?= urlencode($language['id']) ?>"
                                                               class="btn btn-sm btn-outline" title="Edit Language">
                                                                ✏️
                                                            </a>
                                                            <?php if ($language['bookCount'] == 0): ?>
                                                                <button type="button"
                                                                        class="btn btn-sm btn-danger delete-language"
                                                                        data-id="<?= htmlspecialchars($language['id']) ?>"
                                                                        data-name="<?= htmlspecialchars($language['name']) ?>"
                                                                        title="Delete Language">
                                                                    🗑️
                                                                </button>
                                                            <?php else: ?>
                                                                <button type="button"
                                                                        class="btn btn-sm btn-outline"
                                                                        disabled
                                                                        title="Cannot delete - has <?= $language['bookCount'] ?> books">
                                                                    🔒
                                                                </button>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Pagination -->
                                <?php if ($total_languages > $per_page): ?>
                                    <div class="pagination-wrapper">
                                        <?php
                                        $total_pages = ceil($total_languages / $per_page);
                                        $query_params = $_GET;
                                        ?>
                                        <div class="pagination">
                                            <?php if ($page > 1): ?>
                                                <?php $query_params['page'] = $page - 1; ?>
                                                <a href="?<?= http_build_query($query_params) ?>" class="pagination-btn">‹ Previous</a>
                                            <?php endif; ?>

                                            <?php
                                            $start_page = max(1, $page - 2);
                                            $end_page = min($total_pages, $page + 2);

                                            for ($i = $start_page; $i <= $end_page; $i++):
                                                $query_params['page'] = $i;
                                            ?>
                                                <a href="?<?= http_build_query($query_params) ?>"
                                                   class="pagination-btn <?= $i === $page ? 'active' : '' ?>">
                                                    <?= $i ?>
                                                </a>
                                            <?php endfor; ?>

                                            <?php if ($page < $total_pages): ?>
                                                <?php $query_params['page'] = $page + 1; ?>
                                                <a href="?<?= http_build_query($query_params) ?>" class="pagination-btn">Next ›</a>
                                            <?php endif; ?>
                                        </div>
                                        <div class="pagination-info">
                                            Showing <?= number_format(($page - 1) * $per_page + 1) ?> to
                                            <?= number_format(min($page * $per_page, $total_languages)) ?> of
                                            <?= number_format($total_languages) ?> languages
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                <?php elseif ($action === 'add' || $action === 'edit'): ?>
                    <!-- Add/Edit Language Form -->
                    <div class="page-header">
                        <div class="page-header-content">
                            <h2 class="page-title"><?= $action === 'add' ? 'Add New Language' : 'Edit Language' ?></h2>
                            <p class="page-subtitle">
                                <?= $action === 'add' ? 'Enter language information to add to the library system' : 'Update language information' ?>
                            </p>
                        </div>
                        <div class="page-header-actions">
                            <a href="?" class="btn btn-secondary">
                                <span style="margin-right: 0.5rem;">←</span>
                                Back to Languages
                            </a>
                        </div>
                    </div>

                    <form method="POST" class="language-form">
                        <input type="hidden" name="action" value="<?= $action ?>">
                        <?php if ($action === 'edit' && $language_data): ?>
                            <input type="hidden" name="id" value="<?= htmlspecialchars($language_data['id']) ?>">
                        <?php endif; ?>

                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Language Information</h3>
                            </div>
                            <div class="card-body">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="name" class="label required">Language Name</label>
                                        <input type="text"
                                               id="name"
                                               name="name"
                                               class="input"
                                               placeholder="Enter language name"
                                               value="<?= htmlspecialchars($language_data['name'] ?? '') ?>"
                                               required>
                                        <div class="help-text">The primary name of the language (required)</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="nameNepali" class="label">Nepali/Maithili Name</label>
                                        <input type="text"
                                               id="nameNepali"
                                               name="nameNepali"
                                               class="input nepali-input"
                                               placeholder="भाषाको नाम"
                                               value="<?= htmlspecialchars($language_data['nameNepali'] ?? '') ?>">
                                        <div class="help-text">Language name in Nepali/Maithili script (optional)</div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="code" class="label">Language Code</label>
                                    <input type="text"
                                           id="code"
                                           name="code"
                                           class="input"
                                           placeholder="e.g., en, ne, mai, hi"
                                           value="<?= htmlspecialchars($language_data['code'] ?? '') ?>"
                                           pattern="[a-z]{2,3}"
                                           maxlength="3">
                                    <div class="help-text">ISO language code (2-3 lowercase letters, optional). Examples: en (English), ne (Nepali), mai (Maithili)</div>
                                </div>

                                <div class="form-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox"
                                               name="isActive"
                                               value="1"
                                               <?= ($language_data['isActive'] ?? 1) ? 'checked' : '' ?>>
                                        <span class="checkbox-text">Active Language</span>
                                    </label>
                                    <div class="help-text">Uncheck to deactivate this language</div>
                                </div>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <span style="margin-right: 0.5rem;">💾</span>
                                <?= $action === 'add' ? 'Add Language' : 'Update Language' ?>
                            </button>
                            <a href="?" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Mobile Overlay -->
    <div class="mobile-overlay" id="mobile-overlay"></div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Confirm Delete</h3>
                <button type="button" class="modal-close" onclick="closeDeleteModal()">×</button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the language "<span id="deleteLanguageName"></span>"?</p>
                <p class="text-warning">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeDeleteModal()">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">Delete Language</button>
            </div>
        </div>
    </div>

    <script>
        // Mobile Menu and Delete functionality (same as other modules)
        function toggleMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            sidebar.classList.toggle('open');
            overlay.classList.toggle('active');
            document.body.style.overflow = sidebar.classList.contains('open') ? 'hidden' : '';
        }

        function closeMobileMenu() {
            const sidebar = document.querySelector('.admin-sidebar');
            const overlay = document.querySelector('.mobile-overlay');
            sidebar.classList.remove('open');
            overlay.classList.remove('active');
            document.body.style.overflow = '';
        }

        let deleteLanguageId = null;

        function openDeleteModal(id, name) {
            deleteLanguageId = id;
            document.getElementById('deleteLanguageName').textContent = name;
            document.getElementById('deleteModal').style.display = 'flex';
        }

        function closeDeleteModal() {
            deleteLanguageId = null;
            document.getElementById('deleteModal').style.display = 'none';
        }

        function confirmDelete() {
            if (deleteLanguageId) {
                fetch(`?ajax_delete=1&id=${encodeURIComponent(deleteLanguageId)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            window.location.reload();
                        } else {
                            alert('Error: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while deleting the language.');
                    });
            }
            closeDeleteModal();
        }

        document.addEventListener('DOMContentLoaded', () => {
            // Event listeners
            document.getElementById('mobile-menu-toggle')?.addEventListener('click', toggleMobileMenu);
            document.getElementById('mobile-overlay')?.addEventListener('click', closeMobileMenu);
            document.getElementById('mobile-close-btn')?.addEventListener('click', closeMobileMenu);

            document.querySelectorAll('.delete-language').forEach(button => {
                button.addEventListener('click', function() {
                    openDeleteModal(this.getAttribute('data-id'), this.getAttribute('data-name'));
                });
            });

            document.querySelectorAll('.nav-link').forEach(link => {
                link.addEventListener('click', () => {
                    if (window.innerWidth <= 1023) closeMobileMenu();
                });
            });

            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    closeDeleteModal();
                    closeMobileMenu();
                }
            });
        });

        window.addEventListener('resize', () => {
            if (window.innerWidth > 1023) closeMobileMenu();
        });
    </script>
</body>
</html>
